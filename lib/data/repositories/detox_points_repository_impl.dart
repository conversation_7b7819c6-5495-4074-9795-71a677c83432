import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/entities/detox_points.dart';
import '../../domain/entities/reward.dart';
import '../../domain/entities/points_history.dart';
import '../../domain/failure/failure.dart';
import '../../domain/repositories/detox_points_repository.dart';
import '../local/models/detox_points_model.dart';
import '../local/models/reward_model.dart';

/// Implementation of the DetoxPointsRepository interface
/// Consolidates points functionality from DetoxPointsRepository and DashboardRepository
class DetoxPointsRepositoryImpl implements DetoxPointsRepository {
  final SupabaseClient _supabaseClient;
  final Box<DetoxPointsModel> _pointsBox;
  final Box<RewardModel> _rewardsBox;

  DetoxPointsRepositoryImpl({
    required SupabaseClient supabaseClient,
    required Box<DetoxPointsModel> pointsBox,
    required Box<RewardModel> rewardsBox,
  }) : _supabaseClient = supabaseClient,
       _pointsBox = pointsBox,
       _rewardsBox = rewardsBox;

  @override
  Future<Either<Failure, DetoxPoints>> getUserPoints(String userId) async {
    try {
      // Try to get from local cache first
      final cachedPoints = _pointsBox.get(userId);
      if (cachedPoints != null && _isFromToday(cachedPoints.dailyDate)) {
        return Right(cachedPoints.toEntity());
      }

      // Fetch from Supabase if not in cache or outdated
      final response =
          await _supabaseClient
              .from('detox_points')
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      if (response != null) {
        final pointsModel = DetoxPointsModel.fromJson(response);

        // Reset daily points if it's a new day
        final updatedModel = _resetDailyPointsIfNeeded(pointsModel);

        // Save to cache
        await _pointsBox.put(userId, updatedModel);

        return Right(updatedModel.toEntity());
      } else {
        // Create new points record
        return _createNewPointsRecord(userId);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user points: $e');
      }
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, DetoxPoints>> addPoints(
    String userId,
    int points,
    String reason,
  ) async {
    try {
      final currentPointsResult = await getUserPoints(userId);

      return currentPointsResult.fold((failure) => Left(failure), (
        currentPoints,
      ) async {
        final newTotalPoints = currentPoints.totalPoints + points;
        final newLevel = (newTotalPoints / 100).floor() + 1;
        final pointsToNextLevel = (newLevel * 100) - newTotalPoints;

        final updatedPoints = currentPoints.copyWith(
          totalPoints: newTotalPoints,
          level: newLevel,
          availablePoints: currentPoints.availablePoints + points,
          pointsToNextLevel: pointsToNextLevel,
          dailyPoints: currentPoints.dailyPoints + points,
          lastUpdated: DateTime.now(),
        );

        // Update in Supabase
        await _supabaseClient
            .from('detox_points')
            .upsert(DetoxPointsModel.fromEntity(updatedPoints).toJson());

        // Update local cache
        await _pointsBox.put(
          userId,
          DetoxPointsModel.fromEntity(updatedPoints),
        );

        // Log points history
        await _logPointsHistory(userId, points, reason);

        return Right(updatedPoints);
      });
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DetoxPoints>>> getLeaderboard({
    int limit = 10,
  }) async {
    try {
      final response = await _supabaseClient
          .from('detox_points')
          .select()
          .order('total_points', ascending: false)
          .limit(limit);

      final leaderboard =
          response
              .map((json) => DetoxPointsModel.fromJson(json).toEntity())
              .toList();

      return Right(leaderboard);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Reward>>> getAvailableRewards() async {
    try {
      final response = await _supabaseClient
          .from('rewards')
          .select('*, partners(*)')
          .eq('is_available', true)
          .order('created_at', ascending: false);

      final rewards =
          response
              .map((json) => RewardModel.fromJson(json).toEntity())
              .toList();

      return Right(rewards);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Reward>>> getRewardsByCategory(
    RewardCategory category,
  ) async {
    try {
      final response = await _supabaseClient
          .from('rewards')
          .select('*, partners(*)')
          .eq('is_available', true)
          .eq('category', category.toString().split('.').last)
          .order('created_at', ascending: false);

      final rewards =
          response
              .map((json) => RewardModel.fromJson(json).toEntity())
              .toList();

      return Right(rewards);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Reward>>> getRewardsByDistance(
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    try {
      final response = await _supabaseClient.rpc(
        'get_rewards_by_distance',
        params: {'lat': latitude, 'lng': longitude, 'radius_km': radiusKm},
      );

      final rewards =
          response
              .map((json) => RewardModel.fromJson(json).toEntity())
              .toList();

      return Right(rewards);
    } catch (e) {
      // Fallback to all available rewards
      return getAvailableRewards();
    }
  }

  @override
  Future<Either<Failure, bool>> redeemReward(
    String userId,
    String rewardId,
  ) async {
    try {
      final pointsResult = await getUserPoints(userId);

      return pointsResult.fold((failure) => Left(failure), (userPoints) async {
        final rewardResponse =
            await _supabaseClient
                .from('rewards')
                .select()
                .eq('id', rewardId)
                .single();

        final reward = RewardModel.fromJson(rewardResponse).toEntity();

        if (userPoints.availablePoints < reward.pointsEarned) {
          return Left(InsufficientPointsFailure('Not enough points'));
        }

        final updatedPoints = userPoints.copyWith(
          availablePoints: userPoints.availablePoints - reward.pointsEarned,
          lastUpdated: DateTime.now(),
        );

        // Update points
        await _supabaseClient
            .from('detox_points')
            .update({
              'available_points': updatedPoints.availablePoints,
              'last_updated': updatedPoints.lastUpdated.toIso8601String(),
            })
            .eq('user_id', userId);

        // Create redemption record
        await _supabaseClient.from('redemptions').insert({
          'user_id': userId,
          'reward_id': rewardId,
          'points_spent': reward.pointsEarned,
          'redeemed_at': DateTime.now().toIso8601String(),
        });

        // Update local cache
        await _pointsBox.put(
          userId,
          DetoxPointsModel.fromEntity(updatedPoints),
        );

        return const Right(true);
      });
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Reward>>> getUserRedemptionHistory(
    String userId,
  ) async {
    try {
      final response = await _supabaseClient
          .from('redemptions')
          .select('*, rewards!inner(*)')
          .eq('user_id', userId)
          .order('redeemed_at', ascending: false);

      final redemptions =
          response
              .map((json) => RewardModel.fromJson(json['rewards']).toEntity())
              .toList();

      return Right(redemptions);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PointsHistory>> getPointsHistory(String userId) async {
    try {
      final now = DateTime.now();

      // Get points for different time periods
      final last24HoursResponse = await _supabaseClient
          .from('points_history')
          .select('points')
          .eq('user_id', userId)
          .gte(
            'created_at',
            now.subtract(const Duration(hours: 24)).toIso8601String(),
          );

      final lastWeekResponse = await _supabaseClient
          .from('points_history')
          .select('points')
          .eq('user_id', userId)
          .gte(
            'created_at',
            now.subtract(const Duration(days: 7)).toIso8601String(),
          );

      final lastMonthResponse = await _supabaseClient
          .from('points_history')
          .select('points')
          .eq('user_id', userId)
          .gte(
            'created_at',
            now.subtract(const Duration(days: 30)).toIso8601String(),
          );

      final last24Hours = _calculateTotalPoints(last24HoursResponse);
      final lastWeek = _calculateTotalPoints(lastWeekResponse);
      final lastMonth = _calculateTotalPoints(lastMonthResponse);

      return Right(
        PointsHistory(
          last24Hours: last24Hours,
          lastWeek: lastWeek,
          lastMonth: lastMonth,
          dailyData: [], // Can be implemented later if needed
        ),
      );
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  // Helper methods
  bool _isFromToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  DetoxPointsModel _resetDailyPointsIfNeeded(DetoxPointsModel model) {
    if (!_isFromToday(model.dailyDate)) {
      return DetoxPointsModel(
        id: model.id,
        userId: model.userId,
        userName: model.userName,
        avatarUrl: model.avatarUrl,
        totalPoints: model.totalPoints,
        level: model.level,
        availablePoints: model.availablePoints,
        pointsToNextLevel: model.pointsToNextLevel,
        lastUpdated: DateTime.now(),
        dailyPoints: 0, // Reset daily points
        dailyGoalTarget: model.dailyGoalTarget,
        dailyDate: DateTime.now(),
      );
    }
    return model;
  }

  Future<Either<Failure, DetoxPoints>> _createNewPointsRecord(
    String userId,
  ) async {
    try {
      final defaultPoints = DetoxPointsModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        userName: 'User',
        avatarUrl: null,
        totalPoints: 0,
        level: 1,
        availablePoints: 0,
        pointsToNextLevel: 100,
        lastUpdated: DateTime.now(),
        dailyPoints: 0,
        dailyGoalTarget: 50,
        dailyDate: DateTime.now(),
      );

      await _supabaseClient.from('detox_points').insert(defaultPoints.toJson());

      await _pointsBox.put(userId, defaultPoints);

      return Right(defaultPoints.toEntity());
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  Future<void> _logPointsHistory(
    String userId,
    int points,
    String reason,
  ) async {
    try {
      await _supabaseClient.from('points_history').insert({
        'user_id': userId,
        'points': points,
        'reason': reason,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Failed to log points history: $e');
      }
    }
  }

  int _calculateTotalPoints(List<dynamic> response) {
    return response.fold<int>(
      0,
      (total, record) => total + (record['points'] as int? ?? 0),
    );
  }

  // Legacy methods for backward compatibility with DashboardRepository
  Future<int> getDetoxPoints() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = _supabaseClient.auth.currentUser?.id;

    if (userId != null) {
      final result = await getUserPoints(userId);
      return result.fold(
        (failure) => prefs.getInt('detox_points') ?? 0,
        (points) => points.totalPoints,
      );
    }

    return prefs.getInt('detox_points') ?? 0;
  }

  Future<void> updateDetoxPoints(int points) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('detox_points', points);

    final userId = _supabaseClient.auth.currentUser?.id;
    if (userId != null) {
      await addPoints(userId, points, 'Manual update');
    }
  }
}
