import 'package:detoxme/data/local/local_food_mood_data_source.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// Implementation of [LocalFoodMoodDataSource]
class LocalFoodMoodDataSourceImpl implements LocalFoodMoodDataSource {
  /// Box for food recommendations
  final Box<FoodRecommendation> _foodRecommendationsBox;

  /// Box for user votes
  final Box<String> _userVotesBox;

  /// Constructor
  LocalFoodMoodDataSourceImpl({
    required Box<FoodRecommendation> foodRecommendationsBox,
    required Box<String> userVotesBox,
  }) : _foodRecommendationsBox = foodRecommendationsBox,
       _userVotesBox = userVotesBox;

  @override
  Future<List<FoodRecommendation>> getAllFoodRecommendations({
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
  }) async {
    final recommendations = _foodRecommendationsBox.values.toList();
    return _applySortingAndFiltering(
      recommendations,
      sortOption: sortOption,
      excludeTooManyDownvotes: excludeTooManyDownvotes,
    );
  }

  @override
  Future<List<FoodRecommendation>> getFoodRecommendationsForMood(
    MoodType mood, {
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
    String? countryCode,
  }) async {
    var recommendations =
        _foodRecommendationsBox.values
            .where(
              (recommendation) =>
                  recommendation.moodTypes?.contains(mood) == true,
            )
            .toList();

    // Apply country code filter if provided
    if (countryCode != null) {
      recommendations =
          recommendations
              .where(
                (recommendation) => recommendation.countryCode == countryCode,
              )
              .toList();
    }

    return _applySortingAndFiltering(
      recommendations,
      sortOption: sortOption,
      excludeTooManyDownvotes: excludeTooManyDownvotes,
    );
  }

  @override
  Future<FoodRecommendation?> getFoodRecommendationById(String id) async {
    return _foodRecommendationsBox.get(id);
  }

  @override
  Future<void> updateFoodRecommendations(
    List<FoodRecommendation> recommendations,
  ) async {
    final Map<String, FoodRecommendation> recommendationMap = {
      for (var rec in recommendations) rec.id: rec,
    };
    await _foodRecommendationsBox.putAll(recommendationMap);
  }

  @override
  Future<void> updateFoodRecommendation(
    FoodRecommendation recommendation,
  ) async {
    await _foodRecommendationsBox.put(recommendation.id, recommendation);
  }

  @override
  Future<VoteType?> getUserVoteForRecommendation(
    String recommendationId,
  ) async {
    final voteKey = 'vote_$recommendationId';
    final voteStr = _userVotesBox.get(voteKey);

    if (voteStr == null) {
      return null;
    }

    return voteStr == 'upvote' ? VoteType.upvote : VoteType.downvote;
  }

  @override
  Future<Map<String, VoteType>> getAllUserVotes() async {
    final Map<String, VoteType> votes = {};

    // Only process keys that start with 'vote_'
    for (final key in _userVotesBox.keys) {
      if (key is String && key.startsWith('vote_')) {
        final recommendationId = key.substring(5); // Remove 'vote_' prefix
        final voteStr = _userVotesBox.get(key);

        if (voteStr == 'upvote') {
          votes[recommendationId] = VoteType.upvote;
        } else if (voteStr == 'downvote') {
          votes[recommendationId] = VoteType.downvote;
        }
      }
    }

    return votes;
  }

  @override
  Future<void> updateUserVote(
    String recommendationId,
    VoteType? voteType,
  ) async {
    final voteKey = 'vote_$recommendationId';

    if (voteType == null) {
      await _userVotesBox.delete(voteKey);
    } else {
      await _userVotesBox.put(voteKey, voteType.toString());
    }

    // Update the cached recommendation if it exists
    final recommendation = await getFoodRecommendationById(recommendationId);
    if (recommendation != null) {
      final updatedRecommendation = recommendation.copyWith(
        userVote: voteType,
        upvotes:
            voteType == VoteType.upvote
                ? recommendation.upvotes + 1
                : voteType == null && recommendation.userVote == VoteType.upvote
                ? recommendation.upvotes - 1
                : recommendation.upvotes,
        downvotes:
            voteType == VoteType.downvote
                ? recommendation.downvotes + 1
                : voteType == null &&
                    recommendation.userVote == VoteType.downvote
                ? recommendation.downvotes - 1
                : recommendation.downvotes,
      );
      await updateFoodRecommendation(updatedRecommendation);
    }
  }

  @override
  Future<void> updateUserVotes(Map<String, VoteType?> votes) async {
    for (final entry in votes.entries) {
      await updateUserVote(entry.key, entry.value);
    }
  }

  @override
  Future<List<FoodRecommendation>> getFoodRecommendations({
    MoodType? mood,
    String? countryCode,
    int? limit,
    int? offset,
  }) async {
    final recommendations = _foodRecommendationsBox.values.toList();

    // Apply filters
    var filtered =
        recommendations.where((r) {
          // Apply mood filter if provided
          if (mood != null &&
              (r.moodTypes == null || !r.moodTypes!.contains(mood))) {
            return false;
          }

          // Apply country code filter if provided
          if (countryCode != null && r.countryCode != countryCode) {
            return false;
          }

          return true;
        }).toList();

    // Apply pagination
    if (offset != null && limit != null) {
      final start = offset;
      final end = offset + limit;
      if (start < filtered.length) {
        filtered = filtered.sublist(
          start,
          end < filtered.length ? end : filtered.length,
        );
      } else {
        filtered = [];
      }
    }

    return filtered;
  }

  /// Apply sorting and filtering to a list of recommendations
  List<FoodRecommendation> _applySortingAndFiltering(
    List<FoodRecommendation> recommendations, {
    required FoodSortOption sortOption,
    required bool excludeTooManyDownvotes,
  }) {
    var result = List<FoodRecommendation>.from(recommendations);

    // Filter out recommendations with too many downvotes if needed
    if (excludeTooManyDownvotes) {
      result = result.where((r) => r.totalVotes >= 0).toList();
    }

    // Apply sorting
    switch (sortOption) {
      case FoodSortOption.highestVotes:
        result.sort((a, b) => b.totalVotes.compareTo(a.totalVotes));
        break;
      case FoodSortOption.newest:
        result.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case FoodSortOption.oldest:
        result.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case FoodSortOption.default_:
        // No sorting needed for default
        break;
    }

    return result;
  }
}
