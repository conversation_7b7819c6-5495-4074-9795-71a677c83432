import 'package:bloc/bloc.dart';
import 'package:detoxme/core/utils/app_locale_provider.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/food_mood_repository.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// BLoC for the Food Mood feature
class FoodMoodCubit extends Cubit<FoodMoodState> {
  /// Repository for food mood data
  final FoodMoodRepository repository;

  /// App locale provider for language settings
  final AppLocaleProvider? localeProvider;

  /// Flag to track if this cubit is closed
  bool _isClosed = false;
  
  /// Flag to prevent recursive mood updates
  bool _isUpdatingMood = false;

  /// Creates a new [FoodMoodCubit]
  FoodMoodCubit({required this.repository, this.localeProvider})
    : super(const FoodMoodState());

  @override
  Future<void> close() {
    _isClosed = true;
    return super.close();
  }

  /// Safely emit a state only if the cubit is not closed
  void safeEmit(FoodMoodState newState) {
    if (!_isClosed) {
      emit(newState);
    } else if (kDebugMode) {
      print('Attempted to emit on closed FoodMoodCubit, operation skipped');
    }
  }

  /// Load all food recommendations
  Future<void> loadFoodRecommendations({
    String? countryCode,
    FoodSortOption? sortOption,
    bool? excludeTooManyDownvotes,
  }) async {
    if (_isClosed) return;

    // Use provided parameters or current state values
    final finalCountryCode = countryCode ?? state.countryCode;
    final finalSortOption = sortOption ?? state.sortOption;
    final finalExcludeTooManyDownvotes =
        excludeTooManyDownvotes ?? state.excludeTooManyDownvotes;

    safeEmit(state.copyWith(status: FoodMoodStatus.loading));

    try {
      // Attempt to load all recommendations
      final result = await repository.getAllFoodRecommendations(
        sortOption: finalSortOption,
        excludeTooManyDownvotes: finalExcludeTooManyDownvotes,
      );

      result.fold(
        (failure) {
          if (kDebugMode) {
            print('Failed to load food recommendations: ${failure.message}');
          }
          safeEmit(
            state.copyWith(status: FoodMoodStatus.error, failure: failure),
          );
        },
        (foodRecommendations) {
          // Filter by country code if needed
          var filtered = foodRecommendations;
          if (finalCountryCode != null) {
            filtered =
                filtered
                    .where((r) => r.countryCode == finalCountryCode)
                    .toList();
          }

          if (kDebugMode) {
            print(
              'Loaded ${filtered.length} food recommendations (filtered from ${foodRecommendations.length} total)',
            );
          }

          safeEmit(
            state.copyWith(
              status: FoodMoodStatus.loaded,
              foodRecommendations: filtered,
              isFilteredByMood: false,
              countryCode: finalCountryCode,
              sortOption: finalSortOption,
              excludeTooManyDownvotes: finalExcludeTooManyDownvotes,
            ),
          );
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in loadFoodRecommendations: $e');
      }
      safeEmit(
        state.copyWith(
          status: FoodMoodStatus.error,
          failure: ServerFailure('Unexpected error: $e'),
        ),
      );
    }
  }

  /// Load food recommendations for a specific mood
  Future<void> loadFoodRecommendationsForMood(
    MoodType mood, {
    FoodSortOption sortOption = FoodSortOption.default_,
    bool excludeTooManyDownvotes = true,
  }) async {
    // Skip if cubit is closed
    if (_isClosed) {
      if (kDebugMode) {
        print(
          'loadFoodRecommendationsForMood called on closed cubit, skipping',
        );
      }
      return;
    }
    
    // Skip if the mood is the same as current and we're already filtered by mood
    // This helps prevent infinite loops when multiple cubits update each other
    if (state.selectedMood == mood && state.isFilteredByMood) {
      if (kDebugMode) {
        print('Mood is already set to ${mood.toString()}, skipping update');
      }
      return;
    }

    // Debug print to verify mood selection in cubit
    if (kDebugMode) {
      print('Loading food recommendations for mood: ${mood.toString()}');
    }
    
    // Set flag to prevent recursive updates
    _isUpdatingMood = true;

    safeEmit(
      state.copyWith(
        status: FoodMoodStatus.loading,
        selectedMood: mood,
        isFilteredByMood: true,
      ), // Indicate filtering by mood
    );

    // Get current country code from state or locale provider
    String? countryCode = state.countryCode; // Use the single countryCode field

    // If countryCode is null, try to get from localeProvider
    if (countryCode == null && localeProvider != null) {
      countryCode = localeProvider?.locale.languageCode;
      if (kDebugMode) {
        print('Using country code from localeProvider: $countryCode');
      }
    }

    if (kDebugMode) {
      print('Using country code: $countryCode for mood: ${mood.toString()}');
    }

    try {
      // Call repository method that filters by mood and country code
      final result = await repository.getFoodRecommendationsForMood(
        mood,
        // Note: sortOption and excludeTooManyDownvotes are not used by the repository method signature,
        // they are applied in the state's filteredRecommendations getter.
        countryCode: countryCode,
      );

      // Skip if cubit was closed while waiting for the result
      if (_isClosed) return;

      result.fold(
        (failure) {
          if (kDebugMode) {
            print('Error loading food recommendations: ${failure.message}');
          }
          safeEmit(
            state.copyWith(status: FoodMoodStatus.error, failure: failure),
          );
        },
        (recommendations) {
          if (kDebugMode) {
            print(
              'Loaded ${recommendations.length} food recommendations\n'
              'Selected mood: ${mood.toString()}\n'
              'Sort option: ${sortOption.toString()}\n'
              'Exclude downvotes: ${excludeTooManyDownvotes.toString()}\n'
              'Country code: ${countryCode ?? 'None'}',
            );
          }

          safeEmit(
            state.copyWith(
              status: FoodMoodStatus.loaded,
              foodRecommendations: recommendations,
              selectedMood: mood,
              sortOption: sortOption,
              excludeTooManyDownvotes: excludeTooManyDownvotes,
              isFilteredByMood: true,
              countryCode: countryCode, // Keep the country code in state
            ),
          );
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error in loadFoodRecommendationsForMood: $e');
      }

      // Only emit if still active
      if (!_isClosed) {
        safeEmit(
          state.copyWith(
            status: FoodMoodStatus.error,
            failure: ServerFailure('An unexpected error occurred: $e'),
          ),
        );
      }
    } finally {
      // Reset flag
      _isUpdatingMood = false;
    }
  }

  /// Load a single food recommendation by ID
  Future<void> loadFoodRecommendationDetails(String id) async {
    if (_isClosed) return;

    safeEmit(state.copyWith(status: FoodMoodStatus.loading));

    final result = await repository.getFoodRecommendationById(id);

    // Skip if cubit was closed while waiting for the result
    if (_isClosed) return;

    result.fold(
      (failure) =>
          safeEmit(
        state.copyWith(status: FoodMoodStatus.error, failure: failure),
      ),
      (recommendation) => safeEmit(
        state.copyWith(
          status: FoodMoodStatus.loaded,
          selectedFoodRecommendation: recommendation,
        ),
      ),
    );
  }

  /// Set the country code for filtering food recommendations
  void setCountryCode(String? countryCode) {
    emit(state.copyWith(countryCode: countryCode));

    // Apply filter immediately with updated countryCode
    if (state.isFilteredByMood && state.selectedMood != null) {
      loadFoodRecommendationsForMood(
        state.selectedMood!,
        sortOption: state.sortOption,
        excludeTooManyDownvotes: state.excludeTooManyDownvotes,
      );
    } else {
      loadFoodRecommendations(countryCode: countryCode);
    }
  }

  /// Set the sort option
  void applySortOption(FoodSortOption sortOption) {
    emit(state.copyWith(sortOption: sortOption));

    // Apply immediately
    if (state.isFilteredByMood && state.selectedMood != null) {
      loadFoodRecommendationsForMood(
        state.selectedMood!,
        sortOption: sortOption,
        excludeTooManyDownvotes: state.excludeTooManyDownvotes,
      );
    } else {
      loadFoodRecommendations(
        countryCode: state.countryCode,
        sortOption: sortOption,
      );
    }
  }

  /// Toggle whether to exclude food with too many downvotes
  void toggleExcludeTooManyDownvotes({bool? value}) {
    final newValue = value ?? !state.excludeTooManyDownvotes;
    emit(state.copyWith(excludeTooManyDownvotes: newValue));
    
    // Apply immediately
    if (state.isFilteredByMood && state.selectedMood != null) {
      loadFoodRecommendationsForMood(
        state.selectedMood!,
        sortOption: state.sortOption,
        excludeTooManyDownvotes: newValue,
      );
    } else {
      loadFoodRecommendations(
        countryCode: state.countryCode,
        excludeTooManyDownvotes: newValue,
      );
    }
  }

  /// Clear all mood/sort filters (keep country code filter)
  Future<void> clearFilters() async {
    if (_isClosed) return;

    safeEmit(state.clearFilter());
    // Load all recommendations, preserving current country code
    await loadFoodRecommendations(countryCode: state.countryCode);
  }

  /// Reset from loading state back to previous state
  /// Used to recover from timeout situations
  void resetLoadingState() {
    if (_isClosed) return;

    if (state.status == FoodMoodStatus.loading) {
      // Reset to previous stable state
      safeEmit(
        state.copyWith(
          status: FoodMoodStatus.loaded,
          isVoteInProgress: false,
          votingRecommendationId: null,
        ),
      );
    }
  }

  /// Set duration range for filtering
  Future<void> setDurationRange(RangeValues? durationRange) async {
    if (_isClosed) return;

    if (kDebugMode) {
      print('Setting duration range to: $durationRange');
    }

    safeEmit(state.copyWith(durationRange: durationRange));

    // No need to reload data from the server since duration filtering is applied client-side
    // in the filteredRecommendations getter
  }

  /// Vote on a food recommendation (upvote, downvote, or clear vote)
  Future<void> submitVote(String id, VoteType voteType) async {
    if (_isClosed) return;

    // Optimistically update the UI
    final currentRecommendation = state.foodRecommendations.firstWhere(
      (rec) => rec.id == id,
    );

    // Calculate new vote counts based on current vote state
    int newUpvotes = currentRecommendation.upvotes;
    int newDownvotes = currentRecommendation.downvotes;

    // Handle vote changes properly
    VoteType? finalVoteType =
        voteType; // Create a nullable version to track final state

    if (currentRecommendation.userVote == voteType) {
      // User is clicking the same vote type again - toggle it off
      if (voteType == VoteType.upvote) {
        newUpvotes = newUpvotes > 0 ? newUpvotes - 1 : 0;
      } else if (voteType == VoteType.downvote) {
        newDownvotes = newDownvotes > 0 ? newDownvotes - 1 : 0;
      }
      // Set userVote to null when toggling off
      finalVoteType = null;
    } else {
      // User is changing their vote or voting for the first time
      if (currentRecommendation.userVote == VoteType.upvote) {
        // Remove previous upvote
        newUpvotes = newUpvotes > 0 ? newUpvotes - 1 : 0;
      } else if (currentRecommendation.userVote == VoteType.downvote) {
        // Remove previous downvote
        newDownvotes = newDownvotes > 0 ? newDownvotes - 1 : 0;
      }

      // Add the new vote
      if (voteType == VoteType.upvote) {
        newUpvotes++;
      } else if (voteType == VoteType.downvote) {
        newDownvotes++;
      }
    }

    if (kDebugMode) {
      print(
        'Voting on $id: Current vote=${currentRecommendation.userVote}, New vote=$finalVoteType',
      );
      print('Upvotes: ${currentRecommendation.upvotes} -> $newUpvotes');
      print('Downvotes: ${currentRecommendation.downvotes} -> $newDownvotes');
    }

    final updatedRecommendation = currentRecommendation.copyWith(
      userVote: finalVoteType,
      upvotes: newUpvotes,
      downvotes: newDownvotes,
    );

    safeEmit(
      state
          .updateFoodRecommendation(updatedRecommendation)
          .copyWith(isVoteInProgress: true, votingRecommendationId: id),
    );

    try {
      // Submit vote to backend
      // The repository will handle removing the vote if the user clicked the same vote type again
      final result = await repository.submitVote(id, voteType);
      if (_isClosed) return;

      result.fold(
        (failure) {
          if (kDebugMode) {
            print('Vote submission failed: ${failure.message}');
          }
          safeEmit(
            state.copyWith(
              isVoteInProgress: false,
              votingRecommendationId: null,
              failure: failure,
            ),
          );
        },
        (vote) async {
          // Fetch the latest data from the backend
          final recommendationResult = await repository
              .getFoodRecommendationById(id);
          if (_isClosed) return;

          recommendationResult.fold(
            (failure) {
              if (kDebugMode) {
                print(
                  'Failed to fetch updated recommendation: ${failure.message}',
                );
              }
              // Even if fetching fails, we should still clear the loading state
              safeEmit(
                state.copyWith(
                  isVoteInProgress: false,
                  votingRecommendationId: null,
                  failure: failure,
                ),
              );
            },
            (updatedRecommendation) {
              if (kDebugMode) {
                print(
                  'Updated recommendation: upvotes=${updatedRecommendation.upvotes}, downvotes=${updatedRecommendation.downvotes}',
                );
              }
              safeEmit(
                state
                    .updateFoodRecommendation(updatedRecommendation)
                    .copyWith(
                      isVoteInProgress: false,
                      votingRecommendationId: null,
                    ),
              );
            },
          );
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Exception during vote submission: $e');
      }
      // Ensure we clear the loading state even if an exception occurs
      safeEmit(
        state.copyWith(
          isVoteInProgress: false,
          votingRecommendationId: null,
          failure: ServerFailure('Failed to submit vote: $e'),
        ),
      );
    }
  }
}
