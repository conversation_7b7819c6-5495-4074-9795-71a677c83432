import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';

import '../../../domain/entities/reward.dart';
import '../../../domain/entities/points_history.dart';
import '../../../domain/repositories/detox_points_repository.dart';
import 'detox_points_state.dart';
import 'package:detoxme/presentation/bloc/auth/auth_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/auth_state.dart' as auth_state;
import 'package:supabase_flutter/supabase_flutter.dart';

/// Cubit for managing detox points and rewards
class DetoxPointsCubit extends Cubit<DetoxPointsState> {
  final DetoxPointsRepository _detoxPointsRepository;
  final AuthCubit _authCubit;

  // Stream subscription for real-time updates
  StreamSubscription? _pointsSubscription;
  StreamSubscription? _activityCompletionSubscription;

  // Timer for periodic refresh
  Timer? _refreshTimer;

  /// Constructor requiring a DetoxPointsRepository and AuthCubit
  DetoxPointsCubit({
    required DetoxPointsRepository detoxPointsRepository,
    required AuthCubit authCubit,
  }) : _detoxPointsRepository = detoxPointsRepository,
       _authCubit = authCubit,
       super(DetoxPointsState.initial()) {
    // Start listening to auth state changes
    _authCubit.stream.listen((authState) {
      if (authState is auth_state.Authenticated) {
        // User logged in, start subscriptions
        _startSubscriptions(authState.user.id);
        // Note: Removed periodic refresh as we rely on real-time subscriptions
      } else {
        // User logged out, cancel subscriptions
        _cancelSubscriptions();
        _cancelPeriodicRefresh();
      }
    });

    // Initialize subscriptions if user is already authenticated
    if (_currentUserId != null) {
      _startSubscriptions(_currentUserId!);
      // Note: Removed periodic refresh as we rely on real-time subscriptions
    }
  }

  String? get _currentUserId {
    final authState = _authCubit.state;
    if (authState is auth_state.Authenticated) {
      return authState.user.id;
    }
    return null;
  }

  /// Refresh points and history data (shows loading state)
  Future<void> refreshPointsAndHistory() async {
    await loadPointsAndHistory(showLoading: true);
  }

  /// Load the current user's points and history
  Future<void> loadPointsAndHistory({bool showLoading = true}) async {
    final userId = _currentUserId;
    if (userId == null) {
      emit(const DetoxPointsError('User not authenticated.'));
      return;
    }

    // Only show loading state if explicitly requested or if we don't have data yet
    if (showLoading && state is! DetoxPointsLoaded) {
      emit(const DetoxPointsLoading());
    }
    try {
      final pointsResult = await _detoxPointsRepository.getUserPoints(userId);
      final historyResult = await _detoxPointsRepository.getPointsHistory(
        userId,
      );

      pointsResult.fold(
        (failure) =>
            emit(DetoxPointsError('Failed to load points: ${failure.message}')),
        (points) {
          historyResult.fold((historyFailure) {
            // Load points even if history fails
            if (kDebugMode) {
              print(
                'Warning: Failed to load points history: ${historyFailure.message}',
              );
            }
            emit(DetoxPointsLoaded(points));
          }, (history) => emit(DetoxPointsLoaded(points, history: history)));
        },
      );
    } catch (e) {
      emit(DetoxPointsError('An error occurred: ${e.toString()}'));
    }
  }

  /// Load the leaderboard with top users by points
  Future<void> loadLeaderboard({int limit = 10}) async {
    emit(state.copyWith(status: DetoxPointsStatus.loading));

    final result = await _detoxPointsRepository.getLeaderboard(limit: limit);

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: DetoxPointsStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (leaderboard) => emit(
        state.copyWith(
          status: DetoxPointsStatus.success,
          leaderboard: leaderboard,
          clearErrorMessage: true,
        ),
      ),
    );
  }

  /// Load available rewards to redeem
  Future<void> loadAvailableRewards() async {
    emit(state.copyWith(status: DetoxPointsStatus.loading));

    final result = await _detoxPointsRepository.getAvailableRewards();

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: DetoxPointsStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (rewards) => emit(
        state.copyWith(
          status: DetoxPointsStatus.success,
          availableRewards: rewards,
          clearErrorMessage: true,
        ),
      ),
    );
  }

  /// Filter rewards by category
  Future<void> filterRewardsByCategory(RewardCategory category) async {
    emit(
      state.copyWith(
        status: DetoxPointsStatus.loading,
        selectedCategory: category,
      ),
    );

    final result = await _detoxPointsRepository.getRewardsByCategory(category);

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: DetoxPointsStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (rewards) => emit(
        state.copyWith(
          status: DetoxPointsStatus.success,
          availableRewards: rewards,
          clearErrorMessage: true,
        ),
      ),
    );
  }

  /// Load rewards by distance from user location
  Future<void> loadNearbyRewards(double latitude, double longitude) async {
    emit(state.copyWith(status: DetoxPointsStatus.loading));

    final result = await _detoxPointsRepository.getRewardsByDistance(
      latitude,
      longitude,
      state.selectedRadius,
    );

    result.fold(
      (failure) {
        if (kDebugMode) {
          print('Failed to load nearby rewards: ${failure.message}');
        }
        emit(
          state.copyWith(
            status: DetoxPointsStatus.error,
            errorMessage: failure.message,
          ),
        );
      },
      (rewards) => emit(
        state.copyWith(
          status: DetoxPointsStatus.success,
          nearbyRewards: rewards,
          clearErrorMessage: true,
        ),
      ),
    );
  }

  /// Update the search radius for nearby rewards
  Future<void> updateSearchRadius(double radius) async {
    emit(state.copyWith(selectedRadius: radius));
  }

  /// Redeem a reward with user points
  Future<void> redeemReward(String userId, String rewardId) async {
    emit(state.copyWith(status: DetoxPointsStatus.loading));

    final result = await _detoxPointsRepository.redeemReward(userId, rewardId);

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: DetoxPointsStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (_) async {
        // Reload user points after redemption
        final updatedPointsResult = await _detoxPointsRepository.getUserPoints(
          userId,
        );

        updatedPointsResult.fold(
          (failure) => emit(
            state.copyWith(
              status: DetoxPointsStatus.error,
              errorMessage: failure.message,
            ),
          ),
          (updatedPoints) => emit(
            state.copyWith(
              status: DetoxPointsStatus.redeemed,
              userPoints: updatedPoints,
              clearErrorMessage: true,
            ),
          ),
        );
      },
    );
  }

  /// Load the user's redemption history
  Future<void> loadRedemptionHistory(String userId) async {
    emit(state.copyWith(status: DetoxPointsStatus.loading));

    final result = await _detoxPointsRepository.getUserRedemptionHistory(
      userId,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: DetoxPointsStatus.error,
          errorMessage: failure.message,
        ),
      ),
      (history) => emit(
        state.copyWith(
          status: DetoxPointsStatus.success,
          redemptionHistory: history,
          clearErrorMessage: true,
        ),
      ),
    );
  }

  /// Change the selected time period for points display
  void changeTimePeriod(PointsTimePeriod period) {
    emit(state.copyWith(selectedTimePeriod: period));
  }

  /// Toggle between speedometer and bar chart view
  void toggleChartView() {
    emit(state.copyWith(showBarChart: !state.showBarChart));
  }

  /// Reset category filter and load all rewards
  Future<void> resetCategoryFilter() async {
    emit(
      state.copyWith(status: DetoxPointsStatus.loading, selectedCategory: null),
    );

    await loadAvailableRewards();
  }

  /// Add points to the user's account
  Future<void> addPoints(int points, String reason) async {
    final userId = _currentUserId;
    if (userId == null) {
      emit(const DetoxPointsError('User not authenticated.'));
      return;
    }

    // Keep the current state to revert if needed, or show loading
    final currentState = state;
    if (currentState is DetoxPointsLoaded) {
      emit(DetoxPointsLoading()); // Or just update optimistically
    }

    try {
      final result = await _detoxPointsRepository.addPoints(
        userId,
        points,
        reason,
      );
      result.fold(
        (failure) {
          emit(DetoxPointsError('Failed to add points: ${failure.message}'));
          // Revert to previous state if it was loaded
          if (currentState is DetoxPointsLoaded) {
            emit(currentState);
          }
        },
        (updatedPoints) {
          // Reload history along with the updated points without showing loading
          loadPointsAndHistory(showLoading: false);
        },
      );
    } catch (e) {
      emit(
        DetoxPointsError('An error occurred adding points: ${e.toString()}'),
      );
      if (currentState is DetoxPointsLoaded) {
        emit(currentState);
      }
    }
  }

  /// Start subscriptions to real-time updates
  void _startSubscriptions(String userId) {
    if (kDebugMode) {
      print('Starting real-time subscriptions for user $userId');
    }

    // Subscribe to detox_points table changes
    _pointsSubscription = Supabase.instance.client
        .from('detox_points')
        .stream(primaryKey: ['id'])
        .eq('user_id', userId)
        .listen(
          (List<Map<String, dynamic>> data) {
            if (data.isNotEmpty) {
              if (kDebugMode) {
                print('Received real-time update for detox_points: $data');
              }
              // Check if cubit is closed before loading data
              if (!isClosed) {
                // Reload points data without showing loading state to prevent flickering
                loadPointsAndHistory(showLoading: false);
              } else {
                if (kDebugMode) {
                  print('Skipping detox_points update - cubit is closed');
                }
              }
            }
          },
          onError: (error) {
            if (kDebugMode) {
              print('Error in detox_points subscription: $error');
            }
          },
        );

    // Subscribe to activity_completions table changes
    _activityCompletionSubscription = Supabase.instance.client
        .from('activity_completions')
        .stream(primaryKey: ['id'])
        .eq('user_id', userId)
        .listen(
          (List<Map<String, dynamic>> data) {
            if (data.isNotEmpty) {
              if (kDebugMode) {
                print(
                  'Received real-time update for activity_completions: $data',
                );
              }
              // Check if cubit is closed before loading data
              if (!isClosed) {
                // Reload points data without showing loading state to prevent flickering
                loadPointsAndHistory(showLoading: false);
              } else {
                if (kDebugMode) {
                  print('Skipping activity_completions update - cubit is closed');
                }
              }
            }
          },
          onError: (error) {
            if (kDebugMode) {
              print('Error in activity_completions subscription: $error');
            }
          },
        );
  }

  /// Cancel all subscriptions
  void _cancelSubscriptions() {
    if (kDebugMode) {
      print('Cancelling real-time subscriptions');
    }
    _pointsSubscription?.cancel();
    _pointsSubscription = null;

    _activityCompletionSubscription?.cancel();
    _activityCompletionSubscription = null;
  }

  /// Start periodic refresh timer
  void _startPeriodicRefresh() {
    // Cancel any existing timer
    _cancelPeriodicRefresh();

    // Refresh every 2 minutes instead of 30 seconds to reduce UI flashing
    _refreshTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      if (kDebugMode) {
        print('Periodic refresh of points data');
      }
      // Check if cubit is closed before loading data
      if (!isClosed) {
        loadPointsAndHistory(showLoading: false);
      } else {
        // Cancel the timer if the cubit is closed
        _cancelPeriodicRefresh();
      }
    });
  }

  /// Cancel periodic refresh timer
  void _cancelPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  @override
  Future<void> close() {
    _cancelSubscriptions();
    _cancelPeriodicRefresh();
    return super.close();
  }
}
