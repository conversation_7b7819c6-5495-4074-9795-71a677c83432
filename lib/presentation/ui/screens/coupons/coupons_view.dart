import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/domain/entities/coupon.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/domain/entities/partner.dart';
import 'package:detoxme/domain/entities/redemption.dart';
import 'package:detoxme/presentation/bloc/coupon/coupon_cubit.dart';
import 'package:detoxme/presentation/bloc/coupon/coupon_state.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/ui/screens/coupons/widgets/enhanced_coupon_card.dart';
import 'package:detoxme/presentation/ui/screens/coupons/widgets/mood_selector_for_coupons.dart';
import 'package:detoxme/presentation/ui/screens/coupons/widgets/coupon_filter_sheet.dart';
import 'package:detoxme/presentation/ui/screens/coupons/widgets/qr_code_view.dart';
import 'package:detoxme/presentation/ui/screens/coupons/widgets/feedback_dialog.dart';

/// The main view for the coupons screen
class CouponsView extends StatefulWidget {
  /// Creates a [CouponsView]
  const CouponsView({super.key});

  @override
  State<CouponsView> createState() => _CouponsViewState();
}

class _CouponsViewState extends State<CouponsView> {
  final _scrollController = ScrollController();
  bool _isHeaderCollapsed = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // Initialize with the mood from the dashboard when entering the screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Apply the mood selected in the dashboard if available
      final dashboardCubit = context.read<DashboardCubit>();
      final currentMood = dashboardCubit.getCurrentMood();

      if (currentMood != null) {
        // Apply the selected mood from dashboard to the coupon filter
        final couponCubit = context.read<CouponCubit>();
        couponCubit.selectMood(currentMood);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final bool shouldCollapse = _scrollController.offset > 100;
    if (shouldCollapse != _isHeaderCollapsed) {
      setState(() => _isHeaderCollapsed = shouldCollapse);
    }
  }

  void _showQRCodeBottomSheet(BuildContext context, Coupon coupon, Redemption redemption) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
          ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            padding: const EdgeInsets.symmetric(vertical: 24),
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: QRCodeView(
            redemption: redemption,
            coupon: coupon,
            onFeedback: !redemption.hasFeedback
                ? () {
                    Navigator.pop(context); // Close QR code view
                    _showFeedbackDialog(context, redemption);
                  }
                : null,
          ),
        ),
      ),
    );
  }

  void _showFeedbackDialog(BuildContext context, Redemption redemption) {
    showDialog(
      context: context,
      builder: (context) => CouponFeedbackDialog(
        redemption: redemption,
        onSubmit: (rating, comment) {
          context.read<CouponCubit>().provideFeedback(
                redemption.id,
                rating,
                comment: comment,
              );
        },
      ),
    );
  }

  void _showFilterSheet(BuildContext context) {
    // Get the existing cubit instance from the current context
    final couponCubit = context.read<CouponCubit>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (builderContext) {
        // Wrap the CouponFilterSheet with BlocProvider.value to provide
        // the existing cubit instance to the sheet's context
        return BlocProvider.value(
          value: couponCubit, // Pass the existing cubit instance
          child: const CouponFilterSheet(),
        );
      },
    );
  }

  void _navigateToCouponDetails(BuildContext context, Coupon coupon) {
    // Navigate to coupon details using GoRouter
    context.go('/coupon-details/${coupon.id}', extra: coupon);
  }

  void _redeemCoupon(BuildContext context, Coupon coupon) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Coupon einlösen'),
        content: Text(
          'Möchtest du den ${coupon.discount} Coupon von ${coupon.partner.name} wirklich einlösen? '
          'Dies kostet dich ${coupon.pointsRequired} Punkte.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Abbrechen'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<CouponCubit>().redeemCoupon(coupon.id);
            },
            child: const Text('Einlösen'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshData() async {
    if (!mounted) return;

    final couponCubit = context.read<CouponCubit>();
    await couponCubit.loadCoupons();

    // Reload mood-filtered coupons if a mood is selected
    final selectedMood = couponCubit.state.selectedMood;
    if (selectedMood != null) {
      await couponCubit.loadCouponsForMood(selectedMood);
    }

    // Scroll to top and show header on refresh
    if (_isHeaderCollapsed) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseScaffold(
      title: 'Rewards & Coupons',
      titleIconData: Icons.redeem_outlined,
      iconBackgroundColor: theme.colorScheme.tertiary,
      actions: [
        IconButton(
          icon: const Icon(Icons.history),
          onPressed: () {
            context.read<CouponCubit>().loadRedemptionHistory();
          },
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            foregroundColor: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: () => _showFilterSheet(context),
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            foregroundColor: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(width: 8),
      ],
      body: BlocConsumer<CouponCubit, CouponState>(
        listener: (context, state) {
          if (state.status == CouponStatus.failure && state.failure != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.failure!.message),
                backgroundColor: theme.colorScheme.error,
              ),
            );
          } else if (state.status == CouponStatus.success &&
                    state.selectedRedemption != null &&
                    state.selectedCoupon != null) {
            // Show QR code if a coupon was just redeemed
            _showQRCodeBottomSheet(
              context,
              state.selectedCoupon!,
              state.selectedRedemption!,
            );
          }
        },
        builder: (context, state) {
          if (state.status == CouponStatus.initial) {
            // Load coupons if in initial state
            context.read<CouponCubit>().loadCoupons();
            return const Center(child: CircularProgressIndicator());
          }

          if (state.status == CouponStatus.loading &&
              state.coupons.isEmpty &&
              state.redemptions.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          // Show redemption history if that's what's being viewed
          if (state.showOnlyRedeemed && state.redemptions.isNotEmpty) {
            return _buildRedemptionHistory(context, state);
          }

          // Show empty state if no coupons available
          if (state.coupons.isEmpty && state.redemptions.isEmpty) {
            return _buildEmptyState(context);
          }

          // Show available coupons
          return _buildCouponsList(context, state);
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.redeem_outlined,
                size: 40,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Keine Coupons verfügbar',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Schau später nochmal vorbei oder wähle eine Stimmung, um passende Coupons zu finden.',
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                context.read<CouponCubit>().loadCoupons();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Aktualisieren'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCouponsList(BuildContext context, CouponState state) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Animated collapsible header with mood selector
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _isHeaderCollapsed ? 0 : 240,
            child:
                _isHeaderCollapsed
                    ? const SizedBox.shrink()
                    : AnimatedOpacity(
                      duration: const Duration(milliseconds: 200),
                      opacity: 1.0,
                      child: MoodSelectorForCoupons(),
                    ),
          ),
          // Filter info
          if (state.selectedMood != null ||
              state.showOnlyNearby ||
              state.showOnlyRedeemed)
            Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 8,
                bottom: 0,
              ),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  
                  if (state.showOnlyNearby)
                    Chip(
                      label: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.near_me, size: 16),
                          SizedBox(width: 4),
                          Text('In der Nähe'),
                        ],
                      ),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted:
                          () => context.read<CouponCubit>().toggleNearbyFilter(
                            false,
                          ),
                    ),
                ],
              ),
            ),
          // Coupons list with improved layout
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.only(top: 16, bottom: 16),
              itemCount: state.coupons.length,
              itemBuilder: (context, index) {
                final coupon = state.coupons[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: AnimatedOpacity(
                    opacity: 1.0,
                    duration: Duration(milliseconds: 500 + (index * 100)),
                    child: AnimatedSlide(
                      offset: Offset.zero,
                      duration: Duration(milliseconds: 500 + (index * 100)),
                      curve: Curves.easeOutCubic,
                      child: EnhancedCouponCard(
                        coupon: coupon,
                        onTap: () => _navigateToCouponDetails(context, coupon),
                        onUseNow: () => _redeemCoupon(context, coupon),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRedemptionHistory(BuildContext context, CouponState state) {
    final theme = Theme.of(context);
    return RefreshIndicator(
      onRefresh: () async {
        await context.read<CouponCubit>().loadRedemptionHistory();
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(Icons.history, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Einlösungsverlauf',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () {
                    context.read<CouponCubit>().toggleRedeemedFilter(false);
                    context.read<CouponCubit>().loadCoupons();
                  },
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Zurück'),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: state.redemptions.length,
              itemBuilder: (context, index) {
                final redemption = state.redemptions[index];
                // Find the corresponding coupon
                final coupon = state.coupons.firstWhere(
                  (c) => c.id == redemption.couponId,
                  orElse:
                      () => Coupon(
                        id: '',
                        partner: Partner(
                          id: '',
                          name: 'Unknown Partner',
                          category: PartnerCategory.retail,
                          latitude: 0,
                          longitude: 0,
                          commissionRate: 0,
                          address: '',
                        ),
                        description: 'Coupon nicht mehr verfügbar',
                        discount: '',
                        pointsRequired: 0,
                        expirationDate: DateTime.now(),
                        recommendedForMoods: const [],
                      ),
                );

                return Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: theme.colorScheme.shadow.withValues(
                            alpha: 0.1,
                          ),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Material(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                      clipBehavior: Clip.antiAlias,
                      child: InkWell(
                        onTap:
                            () => _showQRCodeBottomSheet(
                              context,
                              coupon,
                              redemption,
                            ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header with gradient
                            Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    theme.colorScheme.primaryContainer,
                                    theme.colorScheme.primaryContainer
                                        .withValues(alpha: 0.7),
                                  ],
                                ),
                              ),
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  // Partner icon
                                  Container(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.surface,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: theme.colorScheme.shadow
                                              .withValues(alpha: 0.2),
                                          blurRadius: 6,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Center(
                                      child: Icon(
                                        coupon.partner.category ==
                                                PartnerCategory.restaurant
                                            ? Icons.restaurant
                                            : coupon.partner.category ==
                                                PartnerCategory.wellness
                                            ? Icons.spa
                                            : Icons.shopping_bag,
                                        color: theme.colorScheme.primary,
                                        size: 24,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  // Partner name and redemption date
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          coupon.partner.name,
                                          style: theme.textTheme.titleMedium
                                              ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                                color:
                                                    theme
                                                        .colorScheme
                                                        .onPrimaryContainer,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.calendar_today,
                                              size: 14,
                                              color: theme
                                                  .colorScheme
                                                  .onPrimaryContainer
                                                  .withValues(alpha: 0.7),
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              _formatDate(
                                                redemption.redeemedAt,
                                              ),
                                              style: theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                    color: theme
                                                        .colorScheme
                                                        .onPrimaryContainer
                                                        .withValues(alpha: 0.7),
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Discount badge
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.primary,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: theme.colorScheme.shadow
                                              .withValues(alpha: 0.2),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Text(
                                      coupon.discount,
                                      style: theme.textTheme.labelLarge
                                          ?.copyWith(
                                            color: theme.colorScheme.onPrimary,
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Content
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    coupon.description,
                                    style: theme.textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(height: 16),

                                  // Feedback and QR code buttons
                                  Row(
                                    children: [
                                      // Feedback section
                                      if (redemption.hasFeedback)
                                        Expanded(
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 8,
                                            ),
                                            decoration: BoxDecoration(
                                              color: theme
                                                  .colorScheme
                                                  .secondaryContainer
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                const Icon(
                                                  Icons.star,
                                                  color: Colors.amber,
                                                  size: 18,
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  'Bewertung: ${redemption.feedbackRating}/5',
                                                  style: theme
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        )
                                      else
                                        Expanded(
                                          child: OutlinedButton.icon(
                                            onPressed:
                                                () => _showFeedbackDialog(
                                                  context,
                                                  redemption,
                                                ),
                                            icon: const Icon(Icons.rate_review),
                                            label: const Text('Bewerten'),
                                            style: OutlinedButton.styleFrom(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 16,
                                                    vertical: 12,
                                                  ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                            ),
                                          ),
                                        ),
                                      const SizedBox(width: 12),
                                      // QR code button
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed:
                                              () => _showQRCodeBottomSheet(
                                                context,
                                                coupon,
                                                redemption,
                                              ),
                                          icon: const Icon(Icons.qr_code),
                                          label: const Text('QR-Code'),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                theme.colorScheme.primary,
                                            foregroundColor:
                                                theme.colorScheme.onPrimary,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 12,
                                            ),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year.toString();
    return '$day.$month.$year';
  }

  // Add listener for coupon cubit mood changes to sync with dashboard
  void _syncMoodWithDashboard(MoodType? mood) {
    final dashboardCubit = context.read<DashboardCubit>();
    dashboardCubit.syncMoodFromOtherCubit(mood);
  }
}
