import 'package:detoxme/core/utils/app_locale_provider.dart';
import 'package:detoxme/core/widgets/base_scaffold.dart';
import 'package:detoxme/core/themes/theme_cubit.dart';
import 'package:detoxme/presentation/bloc/settings/settings_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:provider/provider.dart';

/// Settings screen for app configuration
class SettingsScreen extends StatelessWidget {
  /// Creates a settings screen
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<SharedPreferences>(
      future: SharedPreferences.getInstance(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        return BlocProvider(
          create: (context) => SettingsCubit(snapshot.data!),
          child: const _SettingsView(),
        );
      },
    );
  }
}

class _SettingsView extends StatelessWidget {
  const _SettingsView();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return BaseScaffold.withTitle(
      title: l10n.settingsTitle,
      titleIconData: Icons.settings_rounded,
      showBackButton: true,
      onBackPressed: () => context.pop(),
      showBottomNav: false,
      body: BlocBuilder<SettingsCubit, SettingsState>(
        builder: (context, state) {
          return ListView(
            children: [
              _buildSectionHeader(
                context,
                l10n.appTheme,
                Icons.palette_outlined,
              ),
              _buildThemeSelector(context),

              _buildSectionHeader(context, l10n.language, Icons.language),
              _buildLanguageSelector(context),

              _buildSectionHeader(
                context,
                l10n.notifications,
                Icons.notifications_outlined,
              ),
              _buildNotificationToggle(context, state),
              if (state.allNotificationsEnabled) ...[
                _buildSectionSubheader(context, l10n.notifyAboutActivities),
                _buildNotificationTypeToggle(
                  context,
                  NotificationType.activities,
                  state,
                  l10n.newActivitiesPushNotif,
                ),
                const SizedBox(height: 12),

                _buildSectionSubheader(context, l10n.activityMoodPreferences),
                _buildMoodPreferencesList(context, state),

                _buildSectionSubheader(context, l10n.otherNotifications),
                _buildNotificationTypeToggle(
                  context,
                  NotificationType.coupons,
                  state,
                  l10n.newCouponsPushNotif,
                ),
                _buildNotificationTypeToggle(
                  context,
                  NotificationType.reminders,
                  state,
                  l10n.activityRemindersPushNotif,
                ),
                _buildNotificationTypeToggle(
                  context,
                  NotificationType.motivation,
                  state,
                  l10n.motivationalMessagesPushNotif,
                ),
                _buildNotificationTypeToggle(
                  context,
                  NotificationType.commentLike,
                  state,
                  l10n.commentLikesPushNotif,
                ),
              ],

              const SizedBox(height: 40),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
  ) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 32, 24, 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: theme.colorScheme.primary, size: 24),
          ),
          const SizedBox(width: 16),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionSubheader(BuildContext context, String title) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 8),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildThemeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Card(
        elevation: 0,
        color: theme.colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: theme.colorScheme.outlineVariant.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: BlocBuilder<ThemeCubit, ThemeState>(
            builder: (context, themeState) {
              return Column(
                children: [
                  _buildThemeOption(
                    context,
                    l10n.themeSystem,
                    Icons.brightness_auto,
                    themeState == ThemeState.system,
                    () {
                      context.read<ThemeCubit>().setSystemTheme();
                    },
                  ),
                  _buildThemeOption(
                    context,
                    l10n.themeLight,
                    Icons.light_mode,
                    themeState == ThemeState.light,
                    () {
                      context.read<ThemeCubit>().setLightTheme();
                    },
                  ),
                  _buildThemeOption(
                    context,
                    l10n.themeDark,
                    Icons.dark_mode,
                    themeState == ThemeState.dark,
                    () {
                      context.read<ThemeCubit>().setDarkTheme();
                    },
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    String title,
    IconData icon,
    bool isSelected,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          children: [
            Icon(
              icon,
              color:
                  isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color:
                    isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(Icons.check_circle, color: theme.colorScheme.primary),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSelector(BuildContext context) {
    final theme = Theme.of(context);
    final localeProvider = Provider.of<AppLocaleProvider>(context);
    final currentLocale = localeProvider.locale.languageCode;
    
    // Get all supported languages
    final languages = AppLocaleProvider.getAvailableLanguages();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Card(
        elevation: 0,
        color: theme.colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: theme.colorScheme.outlineVariant.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Column(
          children:
              languages.map((language) {
                final languageCode = language['code']!;
                final languageName = language['name']!;
                final isSelected = currentLocale == languageCode;

                return InkWell(
                  onTap: () async {
                    // Set the new locale and wait for it to complete
                    await localeProvider.setLocale(Locale(languageCode));

                    // Force a rebuild of the context that uses AppLocalizations
                    if (context.mounted) {
                      // Show a confirmation snackbar
                      final l10n = AppLocalizations.of(context)!;
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            '${l10n.language} ${AppLocaleProvider.getLanguageName(languageCode)}',
                          ),
                          backgroundColor: theme.colorScheme.primary,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 12.0,
                    ),
                    child: Row(
                      children: [
                        Text(
                          _getLanguageFlag(languageCode),
                          style: const TextStyle(fontSize: 24),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            languageName,
                            style: theme.textTheme.bodyLarge?.copyWith(
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              color:
                                  isSelected
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: theme.colorScheme.primary,
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }
  
  String _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '🇬🇧';
      case 'de':
        return '🇩🇪';
      case 'ru':
        return '🇷🇺';
      case 'tr':
        return '🇹🇷';
      case 'ar':
        return '🇸🇦';
      default:
        return '🏳️';
    }
  }

  Widget _buildNotificationToggle(BuildContext context, SettingsState state) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Card(
        elevation: 0,
        color: theme.colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: theme.colorScheme.outlineVariant.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: SwitchListTile(
          title: Text(
            l10n.enableNotifications,
            style: theme.textTheme.titleMedium,
          ),
          subtitle: Text(
            l10n.notificationsDescription,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          value: state.allNotificationsEnabled,
          onChanged: (value) {
            context.read<SettingsCubit>().toggleAllNotifications(value);
          },
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 8.0,
          ),
          activeColor: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildNotificationTypeToggle(
    BuildContext context,
    NotificationType type,
    SettingsState state,
    String title,
  ) {
    final theme = Theme.of(context);
    final isEnabled = state.notificationTypePreferences[type] ?? true;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Card(
        elevation: 0,
        color: theme.colorScheme.surface,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: SwitchListTile(
          title: Text(title, style: theme.textTheme.bodyLarge),
          value: isEnabled,
          onChanged: (value) {
            context.read<SettingsCubit>().toggleNotificationType(type, value);
          },
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 4.0,
          ),
          activeColor: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildMoodPreferencesList(BuildContext context, SettingsState state) {
    final l10n = AppLocalizations.of(context)!;
    
    final moodTypes = [
      {'code': 'happy', 'name': 'Happy', 'emoji': '😊'},
      {'code': 'sad', 'name': 'Sad', 'emoji': '😔'},
      {'code': 'anxious', 'name': 'Anxious', 'emoji': '😰'},
      {'code': 'tired', 'name': 'Tired', 'emoji': '😴'},
      {'code': 'focused', 'name': 'Focused', 'emoji': '🧠'},
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children:
            moodTypes.map((mood) {
              final isEnabled =
                  state.moodNotificationPreferences[mood['code']] ?? true;
              return _buildMoodPreferenceItem(
                context,
                mood['code']!,
                mood['name']!,
                mood['emoji']!,
                isEnabled,
              );
            }).toList(),
      ),
    );
  }

  Widget _buildMoodPreferenceItem(
    BuildContext context,
    String code,
    String name,
    String emoji,
    bool isEnabled,
  ) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      color: theme.colorScheme.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: SwitchListTile(
        title: Row(
          children: [
            Text(emoji, style: const TextStyle(fontSize: 22)),
            const SizedBox(width: 12),
            Text(name, style: theme.textTheme.bodyLarge),
          ],
        ),
        value: isEnabled,
        onChanged: (value) {
          context.read<SettingsCubit>().toggleMoodNotification(code, value);
        },
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 4.0,
        ),
        activeColor: theme.colorScheme.primary,
      ),
    );
  }
}
