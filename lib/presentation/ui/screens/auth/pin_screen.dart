import 'package:detoxme/data/local/local_auth_data_source.dart';
import 'package:detoxme/data/repositories/auth_repository_impl.dart';
import 'package:detoxme/presentation/bloc/auth/pin_cubit.dart';
import 'package:detoxme/presentation/bloc/auth/pin_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart'; // For navigation on success
import 'package:detoxme/core/router/app_router.dart'; // For route paths
import 'package:detoxme/localization/app_localizations.dart'; // Import l10n
import 'package:detoxme/domain/failure/failure.dart'; // Import failure types
import 'package:animate_do/animate_do.dart';

class PinScreenWrapper extends StatelessWidget {
  const PinScreenWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    // Instantiate dependencies here - replace with proper DI later
    final localDataSource = LocalAuthDataSourceImpl();
    final authRepository = AuthRepositoryImpl(localDataSource: localDataSource);

    return BlocProvider(
      // Create cubit and call checkPinStatus immediately
      create:
          (context) =>
              PinCubit(authRepository: authRepository)..checkPinStatus(),
      child: const PinScreen(),
    );
  }
}

class PinScreen extends StatelessWidget {
  const PinScreen({super.key});

  final int _pinLength = 4;

  @override
  Widget build(BuildContext context) {
    final pinCubit = context.read<PinCubit>();
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBar(
        // Title will be managed by BlocBuilder
        title: BlocBuilder<PinCubit, PinState>(
          builder: (context, state) {
            String title = l10n.pinScreenEnterTitle; // Default to enter title
            if (state is PinInputUpdate) {
              title =
                  state.isSetupMode
                      ? l10n.pinScreenSetupTitle
                      : l10n.pinScreenEnterTitle;
            } else if (state is PinStatusChecked) {
              title =
                  state.isPinAlreadySet
                      ? l10n.pinScreenEnterTitle
                      : l10n.pinScreenSetupTitle;
            } else if (state is PinVerificationInProgress) {
              title =
                  state.isSetupMode
                      ? l10n.pinScreenSettingPin
                      : l10n.pinScreenVerifyingPin;
            } else if (state is PinVerificationFailure) {
              title =
                  state.isSetupMode
                      ? l10n.pinScreenSetupTitle
                      : l10n.pinScreenEnterTitle; // Reset title
            }
            return Text(title);
          },
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: theme.colorScheme.onSurface,
        centerTitle: true,
      ),
      body: BlocListener<PinCubit, PinState>(
        listener: (context, state) {
          if (state is PinVerificationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.wasSetupFlow
                      ? l10n.pinScreenSuccessSet
                      : l10n.pinScreenSuccessVerified,
                ),
                behavior: SnackBarBehavior.floating,
                backgroundColor: Colors.green,
              ),
            );

            if (!state.wasSetupFlow && context.canPop()) {
              context.pop(true);
            } else if (state.wasSetupFlow) {
              context.go(AppRoutes.dashboard); // Navigate to dashboard
            }
          } else if (state is PinVerificationFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  '${l10n.pinScreenErrorSnackbarPrefix}${state.failure.message}',
                ),
                behavior: SnackBarBehavior.floating,
                backgroundColor: theme.colorScheme.error,
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // Icon and title
              FadeInDown(
                duration: const Duration(milliseconds: 600),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 40),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.lock_outline,
                    size: 60,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),

              // Title text with gradient
              BlocBuilder<PinCubit, PinState>(
                builder: (context, state) {
                  String title = l10n.pinScreenEnterSubtitle;
                  if (state is PinInputUpdate) {
                    title =
                        state.isSetupMode
                            ? l10n.pinScreenSetupSubtitle
                            : l10n.pinScreenEnterSubtitle;
                  } else if (state is PinStatusChecked) {
                    title =
                        state.isPinAlreadySet
                            ? l10n.pinScreenEnterSubtitle
                            : l10n.pinScreenSetupSubtitle;
                  } else if (state is PinVerificationInProgress) {
                    title =
                        state.isSetupMode
                            ? l10n.pinScreenSaving
                            : l10n.pinScreenChecking;
                  } else if (state is PinVerificationFailure) {
                    // Use specific failure titles
                    title =
                        state.failure is IncorrectPinFailure
                            ? l10n.pinScreenIncorrectPin
                            : l10n.pinScreenSetupFailed;
                  }
                  
                  // Display error message directly from failure object
                  String errorMessage = '';
                  if (state is PinVerificationFailure) {
                    errorMessage = state.failure.message;
                  }

                  return Column(
                    children: [
                      FadeInDown(
                        duration: const Duration(milliseconds: 700),
                        child: ShaderMask(
                          shaderCallback:
                              (bounds) => LinearGradient(
                                colors: [
                                  theme.colorScheme.primary,
                                  Color.lerp(
                                    theme.colorScheme.primary,
                                    theme.colorScheme.tertiary,
                                    0.5,
                                  )!,
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ).createShader(bounds),
                          child: Text(
                        title,
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 28,
                              letterSpacing: -0.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      
                      if (errorMessage.isNotEmpty &&
                          state is PinVerificationFailure &&
                          state.failure is! IncorrectPinFailure &&
                          state.failure is! PinNotSetFailure)
                        FadeInUp(
                          duration: const Duration(milliseconds: 300),
                          child: Container(
                            margin: const EdgeInsets.only(top: 16),
                            padding: const EdgeInsets.symmetric(
                              vertical: 10,
                              horizontal: 16,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.error.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: theme.colorScheme.error.withValues(
                                  alpha: 0.3,
                                ),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: theme.colorScheme.error,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Flexible(
                          child: Text(
                            errorMessage,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.colorScheme.error,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  );
                },
              ),
              
              const SizedBox(height: 40),

              // PIN indicators
              FadeInUp(
                delay: const Duration(milliseconds: 300),
                duration: const Duration(milliseconds: 800),
                child: _buildPinIndicators(),
              ),
              
              const SizedBox(height: 50),
              
              // Keypad
              Expanded(
                child: FadeInUp(
                  delay: const Duration(milliseconds: 400),
                  duration: const Duration(milliseconds: 900),
                  child: _buildNumericKeypad(pinCubit, theme),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPinIndicators() {
    return BlocBuilder<PinCubit, PinState>(
      buildWhen:
          (previous, current) =>
              current is PinInputUpdate ||
              (current is PinVerificationFailure &&
                  previous is! PinVerificationFailure) ||
              (current is PinInputUpdate &&
                  previous is PinVerificationFailure),
      builder: (context, state) {
        int currentLength = 0;
        if (state is PinInputUpdate) {
          currentLength = state.enteredPin.length;
        }

        final theme = Theme.of(context);

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(_pinLength, (index) {
            final bool isFilled = index < currentLength;
            
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 10),
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    isFilled ? theme.colorScheme.primary : Colors.transparent,
                border: Border.all(
                  color:
                      isFilled
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline.withValues(alpha: 0.5),
                  width: 2,
                ),
                boxShadow:
                    isFilled
                        ? [
                          BoxShadow(
                            color: theme.colorScheme.primary.withValues(
                              alpha: 0.3,
                            ),
                            blurRadius: 8,
                            spreadRadius: 1,
                            offset: const Offset(0, 2),
                          ),
                        ]
                        : null,
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildNumericKeypad(PinCubit pinCubit, ThemeData theme) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: 12, // 0-9 + backspace + empty slot
      itemBuilder: (context, index) {
        // Bottom row: 0 is in the middle, left is empty, right is backspace
        if (index == 9) return const SizedBox.shrink(); // Empty slot
        if (index == 10) return _buildKeypadButton("0", pinCubit, theme);
        if (index == 11) {
          return _buildBackspaceButton(pinCubit, theme);
        }
        
        // Normal number button (1-9)
        return _buildKeypadButton("${index + 1}", pinCubit, theme);
      },
    );
  }

  Widget _buildKeypadButton(String number, PinCubit pinCubit, ThemeData theme) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => pinCubit.digitEntered(number),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Text(
              number,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackspaceButton(PinCubit pinCubit, ThemeData theme) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => pinCubit.backspacePressed(),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Icon(
              Icons.backspace_outlined,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
      ),
    );
  }
}
