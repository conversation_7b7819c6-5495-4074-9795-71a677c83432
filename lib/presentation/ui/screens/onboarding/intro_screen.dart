import 'package:detoxme/core/constants/app_constants.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:animate_do/animate_do.dart';

/// Introduction screen showing app features and benefits before authentication
class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    // Check for development mode to automatically skip intro in debug mode
    if (kDebugMode) {
      _checkForDevModeSkip();
    }
  }

  Future<void> _checkForDevModeSkip() async {
    // Allow quick bypass of intro screens in debug mode
    final prefs = await SharedPreferences.getInstance();
    final devModeSkipIntro = prefs.getBool('dev_mode_skip_intro') ?? false;

    if (devModeSkipIntro) {
      if (mounted) {
        await _completeIntro();
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _completeIntro() async {
    // Save that intro has been completed
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.introCompletedKey, true);

    // Navigate to auth screen
    if (mounted) {
      context.go(AppRoutes.auth);
    }
  }

  Future<void> _toggleDevModeSkip() async {
    if (!kDebugMode) return;

    final prefs = await SharedPreferences.getInstance();
    final currentSkipValue = prefs.getBool('dev_mode_skip_intro') ?? false;
    await prefs.setBool('dev_mode_skip_intro', !currentSkipValue);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Dev mode skip intro: ${!currentSkipValue ? 'ENABLED' : 'DISABLED'}',
        ),
        behavior: SnackBarBehavior.floating,
      ),
    );

    if (!currentSkipValue) {
      // If enabling, also complete intro immediately
      await _completeIntro();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    // Define intro pages content
    final List<IntroPage> pages = [
      IntroPage(
        title: 'Welcome to DetoxMe',
        description:
            'Your personal companion for a balanced digital lifestyle. Track your well-being and discover what works best for you.',
        image: 'assets/images/intro_1.png',
        color: theme.colorScheme.primary,
      ),
      IntroPage(
        title: 'Activities & Rewards',
        description:
            'Engage in personalized activities based on your mood. Complete challenges, earn points, and unlock exciting rewards along your journey.',
        image: 'assets/images/intro_2.png',
        color: theme.colorScheme.secondary,
      ),
      IntroPage(
        title: 'Food & Mood Connection',
        description:
            'Discover mood-boosting food recommendations. Whether you\'re feeling low, tired, or stressed, we\'ll suggest the right foods to help you feel better.',
        image: 'assets/images/intro_3.png',
        color: theme.colorScheme.tertiary,
      ),
      IntroPage(
        title: 'Join Our Community',
        description:
            'Create and share activities, vote on food recommendations, and learn from others\' experiences. Together, we\'ll discover what works best for everyone.',
        image: 'assets/images/intro_4.png',
        color: theme.colorScheme.error,
      ),
    ];

    return Scaffold(
      body: Stack(
        children: [
          // Page content
          PageView.builder(
            controller: _pageController,
            itemCount: pages.length,
            onPageChanged: (int page) {
              setState(() {
                _currentPage = page;
              });
            },
            itemBuilder: (context, index) {
              return _buildIntroPage(pages[index], theme);
            },
          ),

          // Page indicators and navigation buttons
          Column(
            children: [
              const Spacer(),
              // Page indicators
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  pages.length,
                  (index) => _buildPageIndicator(index == _currentPage, theme),
                ),
              ),
              // Navigation buttons
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24.0,
                  vertical: 28.0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Skip button (only on non-last pages)
                    _currentPage < pages.length - 1
                        ? TextButton(
                          onPressed: _completeIntro,
                          child: Text(
                            'Skip',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                        )
                        : const SizedBox(width: 80),
          
                    // Next/Get Started button
                    ElevatedButton(
                      onPressed: () {
                        if (_currentPage < pages.length - 1) {
                          _pageController.nextPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeIn,
                          );
                        } else {
                          _completeIntro();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32.0,
                          vertical: 16.0,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: Text(
                        _currentPage < pages.length - 1
                            ? 'Next'
                            : 'Get Started',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Developer mode quick access (only in debug mode)
          if (kDebugMode)
            Positioned(
              right: 20,
              top: 60,
              child: GestureDetector(
                onLongPress: _toggleDevModeSkip,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.developer_mode,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildIntroPage(IntroPage page, ThemeData theme) {
    return FadeInUp(
      duration: const Duration(milliseconds: 500),
      child: Container(
        padding: const EdgeInsets.all(40.0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.surface,
              Color.lerp(theme.colorScheme.surface, page.color, 0.15)!,
              Color.lerp(
                theme.colorScheme.surface,
                page.color,
                0.3,
              )!, // Add third color
            ],
            begin: Alignment.topLeft, // Change gradient direction
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            // Image with bounce animation
            BounceInDown(
              duration: const Duration(milliseconds: 1000),
              child: Image.asset(
                page.image,
                height: 300,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 300,
                    width: 300,
                    decoration: BoxDecoration(
                      color: page.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(150),
                    ),
                    child: Icon(Icons.image, size: 100, color: page.color),
                  );
                },
              ),
            ),
            const Spacer(),
            // Text content with fade animations
            FadeInLeft(
              duration: const Duration(milliseconds: 800),
              child: Text(
                page.title,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            FadeInRight(
              duration: const Duration(milliseconds: 800),
              child: Text(
                page.description,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator(bool isActive, ThemeData theme) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      height: isActive ? 12 : 8,
      width: isActive ? 24 : 8, // Make active indicator wider
      decoration: BoxDecoration(
        color:
            isActive
                ? theme.colorScheme.primary
                : theme.colorScheme.primary.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(6),
      ),
    );
  }
}

class IntroPage {
  final String title;
  final String description;
  final String image;
  final Color color;

  IntroPage({
    required this.title,
    required this.description,
    required this.image,
    required this.color,
  });
}
