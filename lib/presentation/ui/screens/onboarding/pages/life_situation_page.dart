import 'package:detoxme/localization/app_localizations.dart';
import 'package:detoxme/presentation/bloc/onboarding/onboarding_cubit.dart';
import 'package:detoxme/presentation/bloc/onboarding/onboarding_state.dart';
import 'package:detoxme/presentation/ui/screens/onboarding/widgets/input_pages/life_situation_selector_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LifeSituationPage extends StatelessWidget {
  const LifeSituationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BlocBuilder<OnboardingCubit, OnboardingState>(
      buildWhen: (previous, current) {
        if (previous is OnboardingContentState &&
            current is OnboardingContentState) {
          return previous.selectedLifeSituation !=
                  current.selectedLifeSituation ||
              previous.validationErrors != current.validationErrors;
        }
        return true;
      },
      builder: (context, state) {
        if (state is! OnboardingContentState) {
          return const Center(child: CircularProgressIndicator());
        }
        
        // Options for life situations
        final options = [
          l10n.lifeSituationIndividual,
          l10n.lifeSituationRelationship,
          l10n.lifeSituationFamily,
          l10n.lifeSituationCustom,
        ];

        return LifeSituationSelectorWidget(
          title: l10n.lifeSituationTitle,
          body: l10n.lifeSituationDescription,
          options: options,
          selectedOption: state.selectedLifeSituation,
          onSelectionChanged: (selected) {
            if (selected != null) {
              context.read<OnboardingCubit>().selectLifeSituation(selected);
            }
          },
          errorText: state.validationErrors['lifeSituation'],
        );
      },
    );
  }
}
