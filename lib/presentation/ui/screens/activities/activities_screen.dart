import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/services/service_locator.dart';
import '../../../../core/utils/app_locale_provider.dart';
import '../../../../core/widgets/base_scaffold.dart';
import '../../../../domain/entities/goal_status.dart';
import '../../../../localization/app_localizations.dart';
import '../../../bloc/activity/activity_cubit.dart';
import '../../../bloc/activity/activity_state.dart';
import '../../../bloc/activity/activity_timer_cubit.dart';
import '../../../bloc/auth/auth_cubit.dart';
import '../../../bloc/dashboard/dashboard_cubit.dart';
import '../../../bloc/dashboard/dashboard_state.dart';
import 'widgets/activity_suggestions.dart';

/// Screen for showing mood-based activity suggestions
class ActivitiesScreen extends StatefulWidget {
  /// Creates a new ActivitiesScreen
  const ActivitiesScreen({super.key});

  @override
  State<ActivitiesScreen> createState() => _ActivitiesScreenState();
}

class _ActivitiesScreenState extends State<ActivitiesScreen> {
  final ScrollController _scrollController = ScrollController();

  // Filter and sort state
  ActivitySortOption _sortOption = ActivitySortOption.newest;
  bool _showOnlyMyActivities = false;

  // Country options
  final Map<String, Map<String, String>> _countryOptions = {
    'en': {'name': 'English', 'flag': '🇬🇧'},
    'de': {'name': 'German', 'flag': '🇩🇪'},
    'ru': {'name': 'Russian', 'flag': '🇷🇺'},
    'tr': {'name': 'Turkish', 'flag': '🇹🇷'},
    'ar': {'name': 'Arabic', 'flag': '🇸🇦'},
  };

  // Selected country codes - will be initialized in initState
  late Set<String> _selectedCountryCodes;

  @override
  void initState() {
    super.initState();

    // Initialize selected country code with the current app locale
    final localeProvider = Provider.of<AppLocaleProvider>(
      context,
      listen: false,
    );
    final currentLanguage = localeProvider.locale.languageCode;
    _selectedCountryCodes = {currentLanguage};

  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }


  Future<void> _refreshData() async {
    if (!mounted) return;

    try {
      final authCubit = context.read<AuthCubit>();
      final userId = authCubit.getCurrentUserId();
      final activityCubit = context.read<ActivityCubit>();

      if (userId != null) {
        // Load activities with current filters and selected country codes
        await activityCubit.filterActivities(
          sortOption: _sortOption,
          countryCodes: _selectedCountryCodes.toList(),
        );

        // Load mood history
        await activityCubit.loadMoodHistory(userId);

        // Reload suggestions if a mood is selected
        final selectedMood = activityCubit.state.selectedMood;
        if (selectedMood != null) {
          await activityCubit.selectMood(selectedMood);
        }

        // Scroll to top on refresh if needed
        if (_scrollController.hasClients && _scrollController.offset > 0) {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.errorRefreshingData(e.toString())),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showFilterSheet() {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    // Initialize duration range from activity cubit if available
    final activityCubit = context.read<ActivityCubit>();
    RangeValues durationRange =
        activityCubit.state.durationRange ?? const RangeValues(5, 120);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                top: 16,
                left: 16,
                right: 16,
                bottom: MediaQuery.of(context).viewInsets.bottom + 16,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          l10n.filterAndSort,
                          style: theme.textTheme.titleLarge,
                        ),
                        const Spacer(),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Show/hide my activities filter
                    Wrap(
                      spacing: 8,
                      children: [
                        FilterChip(
                          label: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.person,
                                size: 16,
                                color: theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 4),
                              Text(l10n.allActivities),
                            ],
                          ),
                          selected: !_showOnlyMyActivities,
                          side: BorderSide(
                            color: theme.colorScheme.outline,
                            width: 0.2,
                          ),
                          onSelected: (selected) {
                            if (selected) {
                              setState(() {
                                _showOnlyMyActivities = false;
                              });
                              _applyFilters();
                            }
                          },
                          backgroundColor:
                              theme.colorScheme.surfaceContainerHighest,
                          selectedColor: theme.colorScheme.primary,
                        ),
                        FilterChip(
                          label: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.star,
                                size: 16,
                                color: theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 4),
                              Text(l10n.myActivities),
                            ],
                          ),
                          selected: _showOnlyMyActivities,
                          side: BorderSide(
                            color: theme.colorScheme.outline,
                            width: 0.2,
                          ),
                          onSelected: (selected) {
                            if (selected) {
                              setState(() {
                                _showOnlyMyActivities = true;
                              });
                              _applyFilters();
                            }
                          },
                          backgroundColor:
                              theme.colorScheme.surfaceContainerHighest,
                          selectedColor: theme.colorScheme.primary,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Sorting options
                    Text(l10n.sortBy, style: theme.textTheme.titleMedium),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        _buildSortChip(
                          context,
                          l10n.newest,
                          ActivitySortOption.newest,
                          setState,
                        ),
                        _buildSortChip(
                          context,
                          l10n.mostPopular,
                          ActivitySortOption.mostPopular,
                          setState,
                        ),
                        _buildSortChip(
                          context,
                          l10n.mostUpvoted,
                          ActivitySortOption.mostUpvoted,
                          setState,
                        ),
                        _buildSortChip(
                          context,
                          l10n.highestRated,
                          ActivitySortOption.highestRated,
                          setState,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Duration slider
                    Text("Duration", style: theme.textTheme.titleMedium),
                    const SizedBox(height: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${durationRange.start.round()} min',
                                style: theme.textTheme.bodySmall,
                              ),
                              Text(
                                '${durationRange.end.round()} min',
                                style: theme.textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ),
                        RangeSlider(
                          values: durationRange,
                          min: 5,
                          max: 120,
                          divisions: 23,
                          labels: RangeLabels(
                            '${durationRange.start.round()} min',
                            '${durationRange.end.round()} min',
                          ),
                          onChanged: (values) {
                            setState(() {
                              // Round to nearest 5 minutes
                              final start = ((values.start / 5).round() * 5)
                                  .clamp(5, 120);
                              final end = ((values.end / 5).round() * 5).clamp(
                                5,
                                120,
                              );
                              durationRange = RangeValues(
                                start.toDouble(),
                                end.toDouble(),
                              );
                            });
                            // Apply filter immediately
                            activityCubit.setDurationRange(durationRange);
                          },
                        ),
                        // Duration presets
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            ChoiceChip(
                              label: const Text('5-30 min'),
                              selected:
                                  durationRange.start == 5 &&
                                  durationRange.end == 30,
                              side: BorderSide(
                                color: theme.colorScheme.outline,
                                width: 0.2,
                              ),
                              onSelected: (selected) {
                                if (selected) {
                                  setState(
                                    () =>
                                        durationRange = const RangeValues(
                                          5,
                                          30,
                                        ),
                                  );
                                  activityCubit.setDurationRange(durationRange);
                                }
                              },
                              backgroundColor:
                                  theme.colorScheme.surfaceContainerHighest,
                              selectedColor: theme.colorScheme.primary,
                            ),
                            ChoiceChip(
                              label: const Text('30-60 min'),
                              selected:
                                  durationRange.start == 30 &&
                                  durationRange.end == 60,
                              side: BorderSide(
                                color: theme.colorScheme.outline,
                                width: 0.2,
                              ),
                              onSelected: (selected) {
                                if (selected) {
                                  setState(
                                    () =>
                                        durationRange = const RangeValues(
                                          30,
                                          60,
                                        ),
                                  );
                                  activityCubit.setDurationRange(durationRange);
                                }
                              },
                              backgroundColor:
                                  theme.colorScheme.surfaceContainerHighest,
                              selectedColor: theme.colorScheme.primary,
                            ),
                            ChoiceChip(
                              label: const Text('60-120 min'),
                              selected:
                                  durationRange.start == 60 &&
                                  durationRange.end == 120,
                              side: BorderSide(
                                color: theme.colorScheme.outline,
                                width: 0.2,
                              ),
                              onSelected: (selected) {
                                if (selected) {
                                  setState(
                                    () =>
                                        durationRange = const RangeValues(
                                          60,
                                          120,
                                        ),
                                  );
                                  activityCubit.setDurationRange(durationRange);
                                }
                              },
                              backgroundColor:
                                  theme.colorScheme.surfaceContainerHighest,
                              selectedColor: theme.colorScheme.primary,
                            ),
                            ChoiceChip(
                              label: const Text('All'),
                              selected:
                                  durationRange.start == 5 &&
                                  durationRange.end == 120,
                              side: BorderSide(
                                color: theme.colorScheme.outline,
                                width: 0.2,
                              ),
                              onSelected: (selected) {
                                if (selected) {
                                  setState(
                                    () =>
                                        durationRange = const RangeValues(
                                          5,
                                          120,
                                        ),
                                  );
                                  activityCubit.setDurationRange(durationRange);
                                }
                              },
                              backgroundColor:
                                  theme.colorScheme.surfaceContainerHighest,
                              selectedColor: theme.colorScheme.primary,
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Language filters
                    Text(l10n.languages, style: theme.textTheme.titleMedium),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          _countryOptions.entries.map((entry) {
                            final isSelected = _selectedCountryCodes.contains(
                              entry.key,
                            );
                            return ChoiceChip(
                              side: BorderSide(
                                color: theme.colorScheme.outline,
                                width: 0.2,
                              ),
                              label: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(entry.value['flag'] ?? ''),
                                  const SizedBox(width: 8),
                                  Text(entry.value['name'] ?? ''),
                                ],
                              ),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  // Clear previous selection and set new one
                                  _selectedCountryCodes.clear();
                                  if (selected) {
                                    _selectedCountryCodes.add(entry.key);
                                  } else {
                                    // If deselecting, default to app's current locale
                                    final localeProvider =
                                        Provider.of<AppLocaleProvider>(
                                          context,
                                          listen: false,
                                        );
                                    _selectedCountryCodes.add(
                                      localeProvider.locale.languageCode,
                                    );
                                  }
                                });
                                _applyFilters();
                              },
                              backgroundColor:
                                  theme.colorScheme.surfaceContainerHighest,
                              selectedColor: theme.colorScheme.primary,
                            );
                          }).toList(),
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSortChip(
    BuildContext context,
    String label,
    ActivitySortOption sortOption,
    StateSetter setState,
  ) {
    final isSelected = _sortOption == sortOption;
    final theme = Theme.of(context);

    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      side: BorderSide(color: theme.colorScheme.outline, width: 0.2),
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _sortOption = sortOption;
          });
          _applyFilters();
        }
      },
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      selectedColor: theme.colorScheme.primary,
    );
  }

  Future<void> _applyFilters() async {
    if (!mounted) return;

    final activityCubit = context.read<ActivityCubit>();
    final authCubit = context.read<AuthCubit>();

    // Get the current user ID if showing only user's activities
    String? userId;
    if (_showOnlyMyActivities) {
      userId = authCubit.getCurrentUserId();
    }

    // Apply filters with selected country codes
    await activityCubit.filterActivities(
      sortOption: _sortOption,
      userId: userId,
      countryCodes: _selectedCountryCodes.toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return BlocProvider(
      create: (context) => serviceLocator<ActivityTimerCubit>(),
      child: MultiBlocListener(
        listeners: [
          BlocListener<ActivityTimerCubit, ActivityTimerState>(
            listenWhen:
                (previous, current) =>
                    previous.goalStatus != current.goalStatus ||
                    (previous.earnedPoints == 0 && current.earnedPoints > 0),
            listener: (context, state) {
              // Show points earned toast
              if (state.earnedPoints > 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('+${state.earnedPoints} points earned!'),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    duration: const Duration(seconds: 2),
                  ),
                );
              }

              // Handle goal status for navigation
              if (state.goalStatus == GoalStatus.milestoneReached ||
                  state.goalStatus == GoalStatus.bothReached) {
                context.push(AppRoutes.milestoneReached);
              } else if (state.goalStatus == GoalStatus.dailyReached) {
                context.push(AppRoutes.dailyGoalReached);
              }
            },
          ),
          // Listen to dashboard mood changes
          BlocListener<DashboardCubit, DashboardState>(
            listenWhen:
                (previous, current) =>
                    previous.currentMood != current.currentMood,
            listener: (context, dashboardState) {
              if (dashboardState.currentMood != null) {
                // Update activity suggestions when dashboard mood changes
                final activityCubit = context.read<ActivityCubit>();
                activityCubit.selectMood(
                  dashboardState.currentMood!,
                );
              }
            },
          ),
        ],
        child: BaseScaffold(
          usePadding: false,
          showBottomNav: true,
          title: l10n.activitiesScreenTitle,
          titleIconData: Icons.psychology_alt_rounded,
          iconBackgroundColor: Theme.of(context).colorScheme.tertiary,
          actions: [
            IconButton(
              icon: Icon(Icons.filter_list, color: Colors.white),
              onPressed: _showFilterSheet,
              tooltip: l10n.filterAndSort,
            ),
          ],
          body: BlocBuilder<ActivityCubit, ActivityState>(
            buildWhen: (previous, current) => previous.status != current.status,
            builder: (context, state) {
              // Show loading indicator on initial load
              if (state.status == ActivityStatus.initial) {
                return const Center(child: CircularProgressIndicator());
              }

              return RefreshIndicator(
                onRefresh: _refreshData,
                child: ActivitySuggestions(
                  scrollController: _scrollController,
                  isHeaderCollapsed: false,
                  showMotivationalMessage: true,
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
