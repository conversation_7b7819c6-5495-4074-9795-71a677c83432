import 'package:detoxme/core/themes/app_themes.dart';
import 'package:detoxme/core/services/service_locator.dart';
import 'package:detoxme/core/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:dotlottie_loader/dotlottie_loader.dart';
import 'package:zo_animated_border/zo_animated_border.dart';
import 'package:flutter_masonry_view/flutter_masonry_view.dart';

import '../../../../../core/router/app_router.dart';
import '../../../../../domain/entities/activity.dart';
import '../../../../../domain/entities/mood.dart';
import '../../../../../localization/app_localizations.dart';
import '../../../../bloc/activity/activity_cubit.dart';
import '../../../../bloc/activity/activity_state.dart';
import '../../../../bloc/dashboard/dashboard_cubit.dart';
import '../../../../bloc/dashboard/dashboard_state.dart';


/// Widget for displaying activity suggestions based on mood
class ActivitySuggestions extends StatefulWidget {
  /// Create a new ActivitySuggestions widget
  const ActivitySuggestions({
    super.key,
    required this.scrollController,
    this.isHeaderCollapsed = false,
    this.showMotivationalMessage = false,
  });

  /// The scroll controller for the list
  final ScrollController scrollController;

  /// Whether the header is collapsed
  final bool isHeaderCollapsed;

  final bool showMotivationalMessage;

  @override
  State<ActivitySuggestions> createState() => _ActivitySuggestionsState();
}

class _ActivitySuggestionsState extends State<ActivitySuggestions>
    with TickerProviderStateMixin {
  // Animation fields for love burst
  bool _showLoveBurst = false;
  Offset _lovePosition = Offset.zero;
  AnimationController? _animationController;
  
  // Staggered animation controller for cards
  AnimationController? _staggerController;
  List<Animation<double>>? _cardAnimations;
  List<Animation<Offset>>? _slideAnimations;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animationController!.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _showLoveBurst = false;
        });
        _animationController!.reset();
      }
    });

    // Initialize stagger animation controller
    _staggerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    // Ensure activities are loaded when the widget is first initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final activityCubit = context.read<ActivityCubit>();

        print(
          'ActivitySuggestions: Current state - activities: ${activityCubit.state.activities?.length}, status: ${activityCubit.state.status}, selectedMood: ${activityCubit.state.selectedMood?.name}',
        );

        // Load activities only if they haven't been loaded yet
        if (activityCubit.state.activities == null ||
            activityCubit.state.activities!.isEmpty ||
            activityCubit.state.status == ActivityStatus.initial) {
          print('ActivitySuggestions: Loading activities...');
          activityCubit.loadActivities();
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController?.dispose();
    _staggerController?.dispose();
    super.dispose();
  }

  void _showLoveBurstAnimation(Offset position) {
    setState(() {
      _showLoveBurst = true;
      _lovePosition = position;
    });
    _animationController!.forward();
  }

  void _initializeCardAnimations(int itemCount) {
    if (_cardAnimations?.length == itemCount) return;

    _cardAnimations = List.generate(itemCount, (index) {
      final start = (index * 0.1).clamp(0.0, 0.8);
      final end = ((index * 0.1) + 0.4).clamp(0.4, 1.0);

      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _staggerController!,
          curve: Interval(start, end, curve: Curves.easeOutCubic),
        ),
      );
    });

    _slideAnimations = List.generate(itemCount, (index) {
      final start = (index * 0.1).clamp(0.0, 0.8);
      final end = ((index * 0.1) + 0.4).clamp(0.4, 1.0);

      return Tween<Offset>(
        begin: const Offset(0, 0.3),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: _staggerController!,
          curve: Interval(start, end, curve: Curves.easeOutCubic),
        ),
      );
    });

    // Start the animation
    _staggerController!.forward();
  }

  void _startActivity(Activity activity) {
    final activityCubit = context.read<ActivityCubit>();
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    if (activityCubit.hasActiveActivity) {
      // Compact error feedback when another activity is running
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          backgroundColor: theme.colorScheme.errorContainer,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.error_outline,
                color: theme.colorScheme.error,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  l10n.completeCurrentActivityFirst,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onErrorContainer,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }

    try {
      activityCubit.startActivity(activity);
      // Compact success feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          backgroundColor: AppColors.primary.withValues(alpha: 0.9),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.play_circle_filled, color: Colors.white, size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${activity.title} started!',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          backgroundColor: theme.colorScheme.error,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Failed to start activity',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _stopActivity(Activity activity) {
    final theme = Theme.of(context);

    // Show motivational confirmation dialog
    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            backgroundColor: theme.colorScheme.surface,
            title: Row(
              children: [
                Icon(
                  Icons.sentiment_dissatisfied,
                  color: theme.colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    "Hold on!",
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Come on, don't give up now! You're doing great - bring it to the end and earn those valuable Detox Points!",
                  style: theme.textTheme.bodyMedium?.copyWith(
                    height: 1.5,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.star, color: AppColors.primary, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        '+${activity.duration} Points waiting for you!',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text(
                  "Keep Going! 💪",
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  _confirmStopActivity(activity);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.errorContainer,
                  foregroundColor: theme.colorScheme.onErrorContainer,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text("Yes, I'm sure - Stop"),
              ),
            ],
            actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          ),
    );
  }

  void _confirmStopActivity(Activity activity) {
    final activityCubit = context.read<ActivityCubit>();
    final theme = Theme.of(context);

    try {
      activityCubit.cancelActivity();

      // Compact cancellation feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          backgroundColor: theme.colorScheme.errorContainer,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.stop_circle, color: theme.colorScheme.error, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Activity stopped',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onErrorContainer,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          backgroundColor: theme.colorScheme.error,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Failed to stop activity',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Stack(
      children: [
        ListView(
          controller: widget.scrollController,
          padding: const EdgeInsets.only(top: 8, bottom: 120),
          children: [
            // Motivational message section (moved here from activities_screen.dart)
            if (widget.showMotivationalMessage)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        theme.colorScheme.primary.withValues(alpha: 0.25),
                        theme.colorScheme.primary.withValues(alpha: 0.15),
                        theme.colorScheme.primary.withValues(alpha: 0.08),
                        theme.colorScheme.surface.withValues(alpha: 0.95),
                      ],
                      stops: const [0.0, 0.3, 0.7, 1.0],
                    ),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: theme.colorScheme.primary.withValues(alpha: 0.4),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: theme.colorScheme.shadow.withValues(alpha: 0.05),
                        blurRadius: 8,
                        spreadRadius: 1,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  theme.colorScheme.primary.withValues(
                                    alpha: 0.2,
                                  ),
                                  theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: theme.colorScheme.primary.withValues(
                                  alpha: 0.3,
                                ),
                                width: 1.5,
                              ),
                            ),
                            child: Icon(
                              Icons.self_improvement,
                              color: theme.colorScheme.primary,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              l10n.timeToDisconnect,
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        l10n.activitiesMotivationalMessage,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          height: 1.4,
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.8,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest
                              .withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: theme.colorScheme.primary,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              l10n.earnPointsForActivities,
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Mood selector if needed (for Activities screen)


            // Did you know card - only show if not showing motivational message to avoid duplication
            if (!widget.showMotivationalMessage)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                child: _buildActivityTipsCard(context),
              ),

            // Activity Suggestions Content
            BlocListener<DashboardCubit, DashboardState>(
              listenWhen:
                  (previous, current) =>
                      previous.currentMood != current.currentMood,
              listener: (context, dashboardState) {
                // Only update activity filtering if explicitly requested via mood selection
                // Don't automatically filter on initial load or when coming from dashboard
                // This allows the activity tab to show all activities by default

                // Reset animations when mood changes
                _staggerController?.reset();
              },
              child: BlocBuilder<ActivityCubit, ActivityState>(
                buildWhen:
                    (previous, current) =>
                        previous.status != current.status ||
                        previous.selectedMood != current.selectedMood ||
                        previous.filteredActivities !=
                            current.filteredActivities ||
                        previous.durationRange != current.durationRange ||
                        previous.activities != current.activities,
                builder: (context, state) {
                  // Reset animations when content changes
                  if (state.status == ActivityStatus.success) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _staggerController?.reset();
                    });
                  }
                  print(
                    'ActivitySuggestions BlocBuilder: status=${state.status}, activities count=${state.activities?.length ?? 0}, selectedMood=${state.selectedMood?.name}',
                  );

                  // If activities are loading, null, or initial state, handle loading
                  if (state.status == ActivityStatus.loading ||
                      state.status == ActivityStatus.initial ||
                      state.activities == null ||
                      (state.activities != null && state.activities!.isEmpty)) {
                    // Trigger loading activities if not already loading
                    if (state.status != ActivityStatus.loading) {
                      print(
                        'ActivitySuggestions: Triggering loadActivities from BlocBuilder',
                      );
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (context.mounted) {
                          context.read<ActivityCubit>().loadActivities();
                        }
                      });
                    }
                    return _buildLoadingActivities(context);
                  }

                // If there's an error loading activities
                if (state.status == ActivityStatus.error) {
                    print(
                      'ActivitySuggestions: Error state - ${state.errorMessage}',
                    );
                  return _buildErrorState(context, state.errorMessage);
                }

                // Get activities - either filtered by mood or all activities
                final activities = _getActivitiesToShow(state);

                // If no activities are available
                if (activities.isEmpty) {
                  return _buildEmptyState(context);
                }

                // Display the activity list
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                      // Section header
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    theme.colorScheme.primary.withValues(
                                      alpha: 0.2,
                                    ),
                                    theme.colorScheme.primary.withValues(
                                      alpha: 0.1,
                                    ),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: theme.colorScheme.primary.withValues(
                                    alpha: 0.3,
                                  ),
                                  width: 1.5,
                                ),
                              ),
                              child: Icon(
                                state.selectedMood != null
                                    ? Icons.emoji_emotions
                                    : Icons.apps,
                                color: theme.colorScheme.primary,
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                state.selectedMood != null
                                    ? 'Activities for ${state.selectedMood!.name}'
                                    : 'All Activities',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    // Activities grid
                    _buildActivitiesGrid(context, activities),
                  ],
                );
              },
              ),
            ),
          ],
        ),

        // Show animation when love button is tapped
        if (_showLoveBurst)
          Positioned(
            left: _lovePosition.dx - 50, // Center the animation
            top: _lovePosition.dy - 50, // Place it slightly above the tap point
            child: SizedBox(
              width: 100,
              height: 100,
              child: DotLottieLoader.fromAsset(
                'assets/animations/heart_burst.lottie',
                frameBuilder: (context, dotlottie) {
                  if (dotlottie != null) {
                    return Lottie.memory(
                      dotlottie.animations.values.single,
                      controller: _animationController,
                      fit: BoxFit.contain,
                    );
                  } else {
                    return const SizedBox.shrink();
                  }
                },
              ),
            ),
          ),
      ],
    );
  }
  
  Widget _buildActivityTipsCard(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.tertiary,
            theme.colorScheme.primary.withValues(alpha: 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                l10n.tipsAndTricks,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            l10n.activityTips,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingActivities(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(l10n.loading, style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String? errorMessage) {
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            l10n.failedToLoadActivities,
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          if (errorMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withAlpha(230),
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              if (context.mounted) {
                final mood = context.read<ActivityCubit>().state.selectedMood;
                if (mood != null) {
                  context.read<ActivityCubit>().selectMood(mood);
                }
              }
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [

          const SizedBox(height: 24),
          Text(
            l10n.noActivitiesFound,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            l10n.noActivitiesForMoodDescription,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withAlpha(180),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActivitiesGrid(BuildContext context, List<Activity> activities) {
    final theme = Theme.of(context);

    // Initialize card animations
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeCardAnimations(activities.length);
    });

    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 80.0),
      child: MasonryView(
        listOfItem: activities,
        numberOfColumn: 2,
        itemPadding: 6.0,
        itemBuilder: (activity) {
          final index = activities.indexOf(activity);
          final activityCubit = context.read<ActivityCubit>();
          final hasActiveActivity = activityCubit.hasActiveActivity;
          final activeActivityId =
              hasActiveActivity ? activityCubit.activeActivity?.id : null;
          final isActive = activeActivityId == activity.id;
          final isDisabled = hasActiveActivity && !isActive;

          Widget activityWidget = _buildCompactActivityCard(
            context,
            activity,
            isActive,
            isDisabled,
            theme,
          );

          // Apply animated border only to active activity
          if (isActive) {
            activityWidget = ZoDualBorder(
              borderWidth: 1.5,
              borderRadius: BorderRadius.circular(20),
              duration: const Duration(seconds: 12),
              glowOpacity: 0.1,
              trackBorderColor: Colors.transparent,
              firstBorderColor: theme.colorScheme.primary,
              secondBorderColor: theme.colorScheme.secondary,
              child: activityWidget,
            );
          }

          // Wrap with staggered animation
          Widget animatedWidget = AnimatedBuilder(
            animation: _staggerController ?? AnimationController(vsync: this),
            builder: (context, child) {
              if (_cardAnimations == null ||
                  _slideAnimations == null ||
                  index >= _cardAnimations!.length) {
                return child!;
              }

              return FadeTransition(
                opacity: _cardAnimations![index],
                child: SlideTransition(
                  position: _slideAnimations![index],
                  child: child!,
                ),
              );
            },
            child: GestureDetector(
              onDoubleTap: () {
                // Get the position of the double tap to show animation
                final RenderBox box = context.findRenderObject() as RenderBox;
                final position = box.localToGlobal(Offset.zero);
                final size = box.size;

                // Calculate center of the card
                final centerX = position.dx + size.width / 2;
                final centerY = position.dy + size.height / 2;

                _showLoveBurstAnimation(Offset(centerX, centerY));

                // Also toggle favorite
                context.read<ActivityCubit>().toggleFavorite(activity);
              },
              child: activityWidget,
            ),
          );

          return animatedWidget;
        },
      ),
    );
  }

  void _showActivityDetails(
    BuildContext context,
    Activity activity,
    ThemeData theme,
  ) {
    context.push(AppRoutes.activityDetail, extra: activity);
  }

  Widget _buildCompactActivityCard(
    BuildContext context,
    Activity activity,
    bool isActive,
    bool isDisabled,
    ThemeData theme,
  ) {
    final l10n = AppLocalizations.of(context)!;
    final categoryColor = activity.category.color;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.25),
            AppColors.primary.withValues(alpha: 0.15),
            AppColors.primary.withValues(alpha: 0.08),
            theme.colorScheme.surface.withValues(alpha: 0.95),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
        border: Border.all(
          color:
              isActive
                  ? theme.colorScheme.primary
                  : AppColors.primary.withValues(alpha: 0.4),
          width: isActive ? 2 : 1.5,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: InkWell(
          onTap: () => _showActivityDetails(context, activity, theme),
          borderRadius: BorderRadius.circular(24),
          child: Stack(
            children: [
              // Enhanced decorative background pattern
              Positioned(
                top: -30,
                right: -30,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        AppColors.primary.withValues(alpha: 0.15),
                        AppColors.primary.withValues(alpha: 0.05),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: -40,
                left: -40,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        AppColors.primary.withValues(alpha: 0.12),
                        AppColors.primary.withValues(alpha: 0.04),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              // Subtle geometric pattern
              Positioned(
                top: 20,
                left: 20,
                child: Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primary.withValues(alpha: 0.3),
                  ),
                ),
              ),
              Positioned(
                top: 35,
                left: 35,
                child: Container(
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primary.withValues(alpha: 0.2),
                  ),
                ),
              ),

              // Main content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with category icon and duration
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                categoryColor.withValues(alpha: 0.2),
                                categoryColor.withValues(alpha: 0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: categoryColor.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            activity.category.icon,
                            color: categoryColor,
                            size: 20,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                theme.colorScheme.primary.withValues(
                                  alpha: 0.9,
                                ),
                                theme.colorScheme.primary.withValues(
                                  alpha: 0.7,
                                ),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: theme.colorScheme.primary.withValues(
                                  alpha: 0.3,
                                ),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.schedule,
                                size: 14,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${activity.duration} min',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Activity title
                    Text(
                      activity.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // Activity description
                    Text(
                      activity.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                        height: 1.4,
                        fontSize: 13,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 12),

                    // Mood indicators (compact)
                    if (activity.recommendedFor.isNotEmpty)
                      Wrap(
                        spacing: 6,
                        runSpacing: 6,
                        children:
                            activity.recommendedFor.take(3).map((mood) {
                              return Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: AppColors.primary.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  mood.emoji,
                                  style: const TextStyle(fontSize: 12),
                                ),
                              );
                            }).toList(),
                      ),

                    const SizedBox(height: 12),

                    // Action button
                    SizedBox(
                      width: double.infinity,
                      height: 36,
                      child:
                          isActive
                              ? ElevatedButton.icon(
                                onPressed: () => _stopActivity(activity),
                                icon: const Icon(Icons.stop, size: 16),
                                label: Text(
                                  l10n.stopButton,
                                  style: const TextStyle(fontSize: 13),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: theme.colorScheme.error,
                                  foregroundColor: theme.colorScheme.onError,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              )
                              : Container(
                                width: double.infinity,
                                height: 36,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors:
                                        isDisabled
                                            ? [
                                              theme.colorScheme.outline
                                                  .withValues(alpha: 0.3),
                                              theme.colorScheme.outline
                                                  .withValues(alpha: 0.2),
                                            ]
                                            : [
                                              AppColors.primary,
                                              AppColors.primary.withValues(
                                                alpha: 0.8,
                                              ),
                                            ],
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(12),
                                    onTap:
                                        isDisabled
                                            ? null
                                            : () => _startActivity(activity),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(12),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          colors: [
                                            Colors.white.withValues(alpha: 0.2),
                                            Colors.transparent,
                                          ],
                                        ),
                                      ),
                                      child: Center(
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 6,
                                                  ),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: Colors.white.withValues(
                                                  alpha: 0.2,
                                                ),
                                              ),
                                              child: Icon(
                                                Icons.play_arrow_rounded,
                                                color:
                                                    isDisabled
                                                        ? theme
                                                            .colorScheme
                                                            .onSurface
                                                            .withValues(
                                                              alpha: 0.4,
                                                            )
                                                        : Colors.white,
                                                size: 14,
                                              ),
                                            ),
    
                                            Text(
                                              l10n.startButton,
                                              style: theme.textTheme.labelMedium
                                                  ?.copyWith(
                                                    color:
                                                        isDisabled
                                                            ? theme
                                                                .colorScheme
                                                                .onSurface
                                                                .withValues(
                                                                  alpha: 0.4,
                                                                )
                                                            : Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 13,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Activity> _getActivitiesToShow(ActivityState state) {
    print(
      'ActivitySuggestions _getActivitiesToShow: filteredActivities=${state.filteredActivities?.length}, activities=${state.activities?.length}, selectedMood=${state.selectedMood?.name}',
    );

    // Priority 1: If we have activities in the main list, always show them
    if (state.activities != null && state.activities!.isNotEmpty) {
      // If there's a selected mood, try to filter
      if (state.selectedMood != null) {
        // Use filtered activities if available
        if (state.filteredActivities != null &&
            state.filteredActivities!.isNotEmpty) {
          print(
            'ActivitySuggestions: Returning filtered activities (${state.filteredActivities!.length})',
          );
          return state.filteredActivities!;
        }
        // Otherwise filter manually
        else {
          final filtered =
              state.activities!
                  .where(
                    (activity) =>
                        activity.recommendedFor.contains(state.selectedMood!),
                  )
                  .toList();
          print(
            'ActivitySuggestions: Manually filtered by mood ${state.selectedMood!.name}: ${filtered.length} activities',
          );
          // If filtering results in empty list, show all activities instead
          if (filtered.isEmpty) {
            print(
              'ActivitySuggestions: No activities for selected mood, showing all activities',
            );
            final shuffledActivities = List<Activity>.from(state.activities!);
            shuffledActivities.shuffle();
            return shuffledActivities;
          }
          return filtered;
        }
      }
      // No mood selected, show all activities
      else {
        final shuffledActivities = List<Activity>.from(state.activities!);
        shuffledActivities.shuffle();
        print(
          'ActivitySuggestions: Returning all activities shuffled (${shuffledActivities.length})',
        );
        return shuffledActivities;
      }
    }

    // Priority 2: If main activities list is empty but we have filtered activities
    if (state.filteredActivities != null &&
        state.filteredActivities!.isNotEmpty) {
      print(
        'ActivitySuggestions: Main activities empty, returning filtered activities (${state.filteredActivities!.length})',
      );
      return state.filteredActivities!;
    }

    // Fallback: no activities available
    print('ActivitySuggestions: No activities available, returning empty list');
    return [];
  }
}
