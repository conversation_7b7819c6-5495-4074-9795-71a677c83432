import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:detoxme/core/themes/app_themes.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/localization/app_localizations.dart';
import 'package:live_activities/live_activities.dart';
import '../../../../../domain/entities/activity.dart';
import '../../../../../domain/entities/activity_comment.dart';
import '../../../../bloc/activity/activity_cubit.dart';
import '../../../../bloc/activity/activity_state.dart';
import '../../../../bloc/reward/reward_cubit.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/activity/activity_timer_cubit.dart';
import 'package:detoxme/core/services/service_locator.dart';
import '../../../../bloc/auth/auth_cubit.dart';

/// IMPORTANT: For localization in this file, use the following property names:
/// - activity.description - For activity description
/// - activity.healthBenefits - For health/detox benefits (NOT detoxBenefits)
/// - activity.recommendedFor - For moods this activity targets (NOT moods or targetedMoods)
///
/// Refer to the Activity class definition for the correct property names.

/// Screen that displays detailed information about an activity
class ActivityDetailScreen extends StatefulWidget {
  /// The activity to display
  final Activity activity;

  /// Creates a new ActivityDetailScreen
  const ActivityDetailScreen({required this.activity, super.key});

  @override
  State<ActivityDetailScreen> createState() => _ActivityDetailScreenState();
}

class _ActivityDetailScreenState extends State<ActivityDetailScreen>
    with TickerProviderStateMixin {
  final LiveActivities _liveActivities = LiveActivities();
  String? _currentActivityId;
  late TabController _tabController;
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _commentFocusNode = FocusNode();

  // Animation-related properties
  bool _isScrolled = false;
  bool _isKeyboardVisible = false;
  AnimationController? _loveController;
  AnimationController? _motivationController;
  bool _showLoveBurst = false;

  // IMPORTANT: Replace with your actual App Group ID configured in Xcode
  final String _appGroupId = 'group.com.detoxme.appgroup';

  @override
  void initState() {
    super.initState();

    // Initialize tab controller
    _tabController = TabController(length: 2, vsync: this);

    // Add scroll listener
    _scrollController.addListener(_onScroll);
    
    // Add keyboard listener
    _commentFocusNode.addListener(_onFocusChange);

    // Initialize live activities asynchronously if on iOS
    if (Platform.isIOS) {
      _initLiveActivities();
    }

    // Set up animation controllers
    _loveController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _loveController!.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() => _showLoveBurst = false);
        _loveController!.reset();
      }
    });

    _motivationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
  }

  void _onScroll() {
    setState(() {
      _isScrolled = _scrollController.offset > 10;
    });
  }

  void _onFocusChange() {
    setState(() {
      _isKeyboardVisible = _commentFocusNode.hasFocus;
    });
  }

  Future<void> _initLiveActivities() async {
    try {
      await _liveActivities.init(appGroupId: _appGroupId);
    } catch (e) {
      debugPrint("Error initializing Live Activities: $e");
      // Continue even if Live Activities initialization fails
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _commentController.dispose();
    _commentFocusNode.removeListener(_onFocusChange);
    _commentFocusNode.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();

    // End any active Live Activity
    if (_currentActivityId != null && Platform.isIOS) {
      _liveActivities.endActivity(_currentActivityId!);
    }

    _loveController?.dispose();
    _motivationController?.dispose();
    super.dispose();
  }

  void _startActivity() {
    final activityCubit = context.read<ActivityCubit>();
    final activityTimerCubit = context.read<ActivityTimerCubit>();

    try {
      // Start the activity in both cubits for proper state management
      activityCubit.startActivity(widget.activity);
      activityTimerCubit.startActivity(widget.activity);

      // Create Live Activity on iOS asynchronously
      if (Platform.isIOS) {
        _createLiveActivity().catchError((e) {
          debugPrint("Error creating live activity: $e");
          // Continue even if Live Activity creation fails
        });
      }

      // Show snackbar to indicate activity started
      if (mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.started(widget.activity.title)),
            duration: const Duration(seconds: 2),
            action: SnackBarAction(
              label: l10n.stopButton,
              onPressed: _cancelActivity,
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint("Error starting activity: $e");
      // Show error message
      if (mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.failedToStartActivity(e.toString())),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _createLiveActivity() async {
    try {
      final activityData = <String, dynamic>{
        'activityTitle': widget.activity.title,
        'activityDescription': widget.activity.description,
        'activityDurationMinutes': widget.activity.duration,
        'activityCategory': widget.activity.category.displayName,
        'endTime':
            DateTime.now()
                .add(Duration(minutes: widget.activity.duration))
                .millisecondsSinceEpoch,
      };

      _currentActivityId = await _liveActivities.createActivity(activityData);
      debugPrint('Live Activity created with ID: $_currentActivityId');
    } catch (e) {
      debugPrint("Error creating Live Activity: $e");
    }
  }

  void _completeActivity() {
    final activityCubit = context.read<ActivityCubit>();
    final activityTimerCubit = context.read<ActivityTimerCubit>();

    // Complete the activity through cubits
    activityCubit.completeActivity();
    activityTimerCubit.completeActivity();

    if (_currentActivityId != null && Platform.isIOS) {
      _liveActivities.endActivity(_currentActivityId!);
      _currentActivityId = null;
    }

    // Award points
    final rewardCubit = context.read<RewardCubit>();
    rewardCubit.activityCompleted(widget.activity);

    // Show completion dialog
    final l10n = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.activityComplete),
            content: Text(
              'Congratulations! You completed "${widget.activity.title}" and earned ${_calculatePoints(widget.activity)} points!',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _cancelActivity() {
    final activityCubit = context.read<ActivityCubit>();
    final activityTimerCubit = context.read<ActivityTimerCubit>();

    try {
      // Cancel in both cubits
      activityCubit.cancelActivity();
      activityTimerCubit.abortActivity();

      if (_currentActivityId != null && Platform.isIOS) {
        _liveActivities.endActivity(_currentActivityId!).catchError((e) {
          debugPrint("Error ending Live Activity: $e");
          // Continue even if ending the Live Activity fails
        });
        _currentActivityId = null;
      }

      if (mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.activityCancelled),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint("Error cancelling activity: $e");

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cancelling activity: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _toggleFavorite() {
    final activityCubit = context.read<ActivityCubit>();
    activityCubit.toggleFavorite(widget.activity);

    final l10n = AppLocalizations.of(context)!;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.activity.isFavorite
              ? l10n.removedFromFavorites
              : l10n.addedToFavorites,
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void triggerLoveBurst() {
    setState(() => _showLoveBurst = true);
    _loveController!.forward();
  }

  void _upvoteActivity() {
    final activityCubit = context.read<ActivityCubit>();

    // Use the cubit to handle voting instead of setState
    if (widget.activity.hasUserUpvoted) {
      activityCubit.voteActivity(
        widget.activity.copyWith(hasUserUpvoted: false),
        isUpvote: true,
      );
    } else {
      activityCubit.voteActivity(
        widget.activity.copyWith(hasUserUpvoted: true, hasUserDownvoted: false),
        isUpvote: true,
      );
      triggerLoveBurst();
    }
  }

  void _downvoteActivity() {
    final activityCubit = context.read<ActivityCubit>();

    // Use the cubit to handle voting instead of setState
    if (widget.activity.hasUserDownvoted) {
      activityCubit.voteActivity(
        widget.activity.copyWith(hasUserDownvoted: false),
        isUpvote: false,
      );
    } else {
      activityCubit.voteActivity(
        widget.activity.copyWith(hasUserDownvoted: true, hasUserUpvoted: false),
        isUpvote: false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => serviceLocator<ActivityTimerCubit>()),
      ],
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        resizeToAvoidBottomInset: true,
        body: BlocConsumer<ActivityCubit, ActivityState>(
          listener: (context, state) {
            // Handle activity state changes
            if (state.status == ActivityStatus.error) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.errorMessage ?? 'An error occurred'),
                  backgroundColor: theme.colorScheme.error,
                ),
              );
            }
          },
          builder: (context, activityState) {
            return BlocConsumer<ActivityTimerCubit, ActivityTimerState>(
              listener: (context, timerState) {
                // Handle timer state changes
                if (timerState.isCompleted) {
                  _completeActivity();
                }
              },
              builder: (context, timerState) {
                // Determine if the activity is active
                final isActive =
                    activityState.activeActivityId == widget.activity.id ||
                    timerState.currentActivity?.id == widget.activity.id;

                return GestureDetector(
                  onTap: () {
                    // Dismiss keyboard when tapping outside
                    if (_commentFocusNode.hasFocus) {
                      _commentFocusNode.unfocus();
                    }
                  },
                  child: NestedScrollView(
                    controller: _scrollController,
                    headerSliverBuilder: (context, innerBoxIsScrolled) {
                      return [
                        // Enhanced Sticky app bar with beautiful header
                        SliverAppBar(
                          expandedHeight: _isKeyboardVisible ? 0.0 : 300.0,
                          floating: false,
                          pinned: true,
                          snap: false,
                          backgroundColor: AppColors.primary,
                          elevation: 0,
                          flexibleSpace:
                              _isKeyboardVisible
                                  ? null
                                  : FlexibleSpaceBar(
                                    background: _buildEnhancedHeader(
                                      context,
                                      activityState,
                                      isActive,
                                    ),
                                  ),
                          leading: Container(
                            margin: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.black26,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: IconButton(
                              icon: Icon(Icons.arrow_back, color: Colors.white),
                              onPressed: () => Navigator.of(context).pop(),
                            ),
                          ),
                          title: AnimatedOpacity(
                            opacity:
                                _isScrolled || _isKeyboardVisible ? 1.0 : 0.0,
                            duration: const Duration(milliseconds: 200),
                            child: Text(
                              widget.activity.title,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          centerTitle: true,
                          actions: [
                            _buildSimpleFavoriteButton(
                              context,
                              widget.activity,
                            ),
                          ],
                        ),

                        // Enhanced Tab bar
                        if (!_isKeyboardVisible)
                          SliverPersistentHeader(
                            delegate: _SliverTabBarDelegate(
                              TabBar(
                                controller: _tabController,
                                tabs: [
                                  Tab(
                                    text: AppLocalizations.of(context)!.details,
                                  ),
                                  Tab(
                                    text:
                                        AppLocalizations.of(
                                          context,
                                        )!.discussion,
                                  ),
                                ],
                                labelColor: AppColors.primary,
                                labelStyle: theme.textTheme.bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                      letterSpacing: 0.5,
                                    ),
                                unselectedLabelColor: theme
                                    .colorScheme
                                    .onSurfaceVariant
                                    .withValues(alpha: 0.6),
                                unselectedLabelStyle: theme.textTheme.bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                      letterSpacing: 0.3,
                                    ),
                                indicatorColor: AppColors.primary,
                                indicatorWeight: 2,
                                indicatorSize: TabBarIndicatorSize.label,
                                indicatorPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                ),
                                labelPadding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 12,
                                ),
                                dividerColor: Colors.transparent,
                                overlayColor: WidgetStateProperty.resolveWith<
                                  Color?
                                >((Set<WidgetState> states) {
                                  if (states.contains(WidgetState.hovered)) {
                                    return AppColors.primary.withValues(
                                      alpha: 0.08,
                                    );
                                  }
                                  if (states.contains(WidgetState.pressed)) {
                                    return AppColors.primary.withValues(
                                      alpha: 0.12,
                                    );
                                  }
                                  return null;
                                }),
                              ),
                              backgroundColor: theme.scaffoldBackgroundColor
                                  .withValues(alpha: 0.95),
                            ),
                            pinned: true,
                          ),
                      ];
                    },
                    body:
                        _isKeyboardVisible
                            ? _buildEnhancedDiscussionTab(
                              context,
                              activityState,
                            )
                            : TabBarView(
                              controller: _tabController,
                              children: [
                                // Enhanced Info tab
                                Stack(
                                  children: [
                                    _buildEnhancedInfoTab(context),

                                    // Enhanced bottom action button
                                    Positioned(
                                      bottom: 20,
                                      left: 20,
                                      right: 20,
                                      child: _buildEnhancedActionButton(
                                        context,
                                        isActive,
                                        timerState.isRunning,
                                      ),
                                    ),
                                  ],
                                ),

                                // Enhanced Discussion tab
                                _buildEnhancedDiscussionTab(
                                  context,
                                  activityState,
                                ),
                              ],
                            ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader(
    BuildContext context,
    ActivityState state,
    bool isActive,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final activity = _getActivityWithLatestStatus(state);

    return Stack(
      children: [
        // Background image with enhanced gradient overlay
        Positioned.fill(
          child:
              activity.imageAsset != null
                  ? Stack(
                    children: [
                      Positioned.fill(
                        child: Image.asset(
                          activity.imageAsset!,
                          fit: BoxFit.cover,
                          errorBuilder:
                              (context, error, stackTrace) =>
                                  _buildImagePlaceholder(
                                    activity.category,
                                    colorScheme,
                                  ),
                        ),
                      ),
                      // Enhanced gradient overlay with motivational feel
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                AppColors.primary.withValues(alpha: 0.48),
                                AppColors.primary.withValues(alpha: 0.8),
                                Colors.black.withValues(alpha: 0.04),
                              ],
                              stops: [0.0, 0.7, 1.0],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                  : activity.imageUrl != null
                  ? Stack(
                    children: [
                      Positioned.fill(
                        child: CachedNetworkImage(
                          imageUrl: activity.imageUrl!,
                          fit: BoxFit.cover,
                          maxWidthDiskCache: 800,
                          maxHeightDiskCache: 400,
                          memCacheWidth: 800,
                          memCacheHeight: 400,
                          useOldImageOnUrlChange: true,
                          placeholder:
                              (context, url) => _buildImagePlaceholder(
                                activity.category,
                                colorScheme,
                              ),
                          errorWidget:
                              (context, error, stackTrace) =>
                                  _buildImagePlaceholder(
                                    activity.category,
                                    colorScheme,
                                  ),
                        ),
                      ),
                      // Enhanced gradient overlay
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                AppColors.primary.withValues(alpha: 0.48),
                                AppColors.primary.withValues(alpha: 0.8),
                                Colors.black.withValues(alpha: 0.04),
                              ],
                              stops: [0.0, 0.7, 1.0],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                  : _buildImagePlaceholder(activity.category, colorScheme),
        ),

        // Activity status indicator
        if (isActive)
          Positioned(
            top: 60,
            left: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 6,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'ACTIVE',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),

        // Simple voting buttons moved to right
        Positioned(
          top: 120,
          right: 20,
          child: Column(
            children: [
              _buildSimpleVoteButton(
                context,
                Icons.thumb_up,
                activity.upvotes,
                activity.hasUserUpvoted,
                true,
                _upvoteActivity,
              ),
              const SizedBox(height: 12),
              _buildSimpleVoteButton(
                context,
                Icons.thumb_down,
                activity.downvotes,
                activity.hasUserDownvoted,
                false,
                _downvoteActivity,
              ),
            ],
          ),
        ),

        // Enhanced title and info section
        Positioned(
          left: 20,
          right: 20,
          bottom: 20,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Category badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.25),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.4),
                  ),
                ),
                child: Text(
                  activity.category.displayName.toUpperCase(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
              ),
              const SizedBox(height: 8),

              // Title with enhanced styling
              Text(
                activity.title,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.w900,
                  color: Colors.white,
                  height: 1.2,
                  shadows: [
                    Shadow(
                      color: Colors.black54,
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Enhanced info row
              Row(
                children: [
                  // Duration
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.25),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.4),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.timer_outlined,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '${activity.duration} min',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Difficulty
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.25),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.4),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getDifficultyIcon(activity.difficultyLevel),
                          color: Colors.white,
                          size: 18,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          activity.difficultyLevel.displayName,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const Spacer(),

                  // Points indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 1.0),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.withValues(alpha: 1.0),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.stars, color: Colors.white, size: 20),
                        const SizedBox(width: 6),
                        Text(
                          '${_calculatePoints(activity)} pts',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Love burst animation
        if (_showLoveBurst)
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _loveController!,
              builder: (context, child) {
                return CustomPaint(
                  painter: LoveBurstPainter(_loveController!.value),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildSimpleFavoriteButton(BuildContext context, Activity activity) {
    return Container(
      margin: const EdgeInsets.only(right: 20),
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          _toggleFavorite();
        },
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.25),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.4),
              width: 1,
            ),
          ),
          child: Icon(
            activity.isFavorite ? Icons.favorite : Icons.favorite_border,
            color: activity.isFavorite ? Colors.red : Colors.white,
            size: 22,
          ),
        ),
      ),
    );
  }

  Widget _buildSimpleVoteButton(
    BuildContext context,
    IconData icon,
    int count,
    bool isSelected,
    bool isUpvote,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.25),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.4),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color:
                  isSelected
                      ? (isUpvote ? Colors.green : Colors.red)
                      : Colors.white,
              size: 20,
            ),
            if (count > 0) ...[
              const SizedBox(height: 2),
              Text(
                count.toString(),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 11,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getDifficultyIcon(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return Icons.star_border;
      case DifficultyLevel.intermediate:
        return Icons.star_half;
      case DifficultyLevel.expert:
        return Icons.star;
    }
  }

  Widget _buildEnhancedInfoTab(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Show creator information if available
          if (widget.activity.createdBy != null)
            Text(
              l10n.createdBy(widget.activity.createdBy ?? ''),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          if (widget.activity.createdBy != null)
            const SizedBox(height: 16),

          // Description
          _buildSection(
            context,
            title: l10n.description,
            child: Text(
              widget.activity.description,
              style: theme.textTheme.bodyLarge,
            ),
          ),
          const SizedBox(height: 24),

          // Health Benefits as tags - only show if the Activity has it
          if (widget.activity.healthBenefits.isNotEmpty)
            _buildSection(
              context,
              title: l10n.healthBenefits,
              child: _buildHealthBenefitsTags(context),
            ),
          if (widget.activity.healthBenefits.isNotEmpty)
            const SizedBox(height: 24),

          // Moods - only show if the Activity has it
          if (widget.activity.recommendedFor.isNotEmpty)
            _buildSection(
              context,
              title: l10n.recommendedForMoods,
              child: _buildMoodsList(context),
            ),
          if (widget.activity.recommendedFor.isNotEmpty)
            const SizedBox(height: 24),

          // Space to ensure the last item clears the FAB
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildEnhancedDiscussionTab(
    BuildContext context,
    ActivityState state,
  ) {
    final theme = Theme.of(context);
    final activityCubit = context.read<ActivityCubit>();
    final activityColor = AppColors.primary;

    return Column(
      children: [
        // Comments list
        Expanded(
          child: StreamBuilder<List<ActivityComment>>(
            stream: activityCubit.getCommentsStreamForActivity(
              widget.activity.id,
            ),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting &&
                  !snapshot.hasData) {
                return Center(
                  child: CircularProgressIndicator(color: activityColor),
                );
              } else if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Failed to load comments: ${snapshot.error}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                // Show empty state
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: activityColor.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.comment_outlined,
                          size: 64,
                          color: activityColor.withValues(alpha: 0.7),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'No comments yet',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Be the first to share your thoughts',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                // Display the comments list
                final comments = snapshot.data!;
                return ListView.builder(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 42),
                  physics: const BouncingScrollPhysics(),
                  itemCount: comments.length,
                  itemBuilder: (context, index) {
                    final comment = comments[index];
                    return _buildEnhancedCommentCard(
                      context,
                      comment,
                      activityColor,
                      activityCubit,
                    );
                  },
                );
              }
            },
          ),
        ),

        // Compact comment input
        Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 8,
          ),
          color: theme.colorScheme.surface,
          child: SafeArea(
            top: false,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Text input
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            _commentFocusNode.hasFocus
                                ? activityColor
                                : Colors.transparent,
                        width: 1,
                      ),
                    ),
                    child: TextField(
                      controller: _commentController,
                      focusNode: _commentFocusNode,
                      decoration: InputDecoration(
                        hintText:
                            AppLocalizations.of(context)!.shareYourExperience,
                        hintStyle: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      maxLines: 3,
                      minLines: 1,
                      textCapitalization: TextCapitalization.sentences,
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ),
                const SizedBox(width: 8),

                // Send button
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  child: Material(
                    color:
                        _commentController.text.trim().isNotEmpty
                            ? activityColor
                            : theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(16),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: () {
                        if (_commentController.text.trim().isNotEmpty) {
                          activityCubit.addComment(
                            activityId: widget.activity.id,
                            text: _commentController.text.trim(),
                          );
                          _commentController.clear();
                          _commentFocusNode.unfocus();

                          // Add haptic feedback
                          HapticFeedback.lightImpact();
                        }
                      },
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Icon(
                          Icons.send_rounded,
                          color:
                              _commentController.text.trim().isNotEmpty
                                  ? Colors.white
                                  : theme.colorScheme.onSurfaceVariant
                                      .withValues(alpha: 0.6),
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedCommentCard(
    BuildContext context,
    ActivityComment comment,
    Color activityColor,
    ActivityCubit activityCubit,
  ) {
    final theme = Theme.of(context);
    final authCubit = context.read<AuthCubit>();
    final userId = authCubit.getCurrentUserId();
    final isOwnComment = comment.userId == userId;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16)),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onLongPress:
              () => _showCommentOptions(
                context,
                comment,
                isOwnComment,
                activityCubit,
              ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Comment header
                Row(
                  children: [
                    // Avatar
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            activityColor.withValues(alpha: 0.6),
                            activityColor.withValues(alpha: 1.0),
                          ],
                        ),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: activityColor.withValues(alpha: 0.8),
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          comment.userName.isNotEmpty
                              ? comment.userName[0].toUpperCase()
                              : '?',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),

                    // User info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                comment.userName,
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: activityColor,
                                ),
                              ),
                              if (isOwnComment) ...[
                                const SizedBox(width: 6),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: activityColor.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    'You',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: activityColor,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 10,
                                    ),
                                  ),
                                ),
                              ],
                              if (comment.isEdited) ...[
                                const SizedBox(width: 6),
                                Text(
                                  '(edited)',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface
                                        .withValues(alpha: 0.6),
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          Text(
                            _formatDetailedDate(comment.createdAt),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.8,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // More options button
                    IconButton(
                      onPressed:
                          () => _showCommentOptions(
                            context,
                            comment,
                            isOwnComment,
                            activityCubit,
                          ),
                      icon: Icon(
                        Icons.more_vert,
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.8,
                        ),
                        size: 20,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),

                // Comment text
                Text(
                  comment.text,
                  style: theme.textTheme.bodyMedium?.copyWith(height: 1.5),
                ),
                
                const SizedBox(height: 12),

                // Comment actions
                Row(
                  children: [
                    // Like button with animation
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(20),
                        onTap: () {
                          activityCubit.toggleCommentLike(
                            commentId: comment.id,
                            activityId: widget.activity.id,
                          );
                          HapticFeedback.lightImpact();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color:
                                comment.isLikedByUser
                                    ? Colors.red.withValues(alpha: 0.4)
                                    : Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color:
                                  comment.isLikedByUser
                                      ? Colors.red.withValues(alpha: 1.0)
                                      : theme.dividerColor,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                comment.isLikedByUser
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color:
                                    comment.isLikedByUser
                                        ? Colors.red
                                        : theme.colorScheme.onSurface
                                            .withValues(alpha: 0.8),
                                size: 18,
                              ),
                              if (comment.likes > 0) ...[
                                const SizedBox(width: 6),
                                Text(
                                  comment.likes.toString(),
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color:
                                        comment.isLikedByUser
                                            ? Colors.red
                                            : theme.colorScheme.onSurface
                                                .withValues(alpha: 0.8),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 16),

                    // Long press hint
                    Text(
                      'Hold for options',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.8,
                        ),
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showCommentOptions(
    BuildContext context,
    ActivityComment comment,
    bool isOwnComment,
    ActivityCubit activityCubit,
  ) {
    final theme = Theme.of(context);
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: theme.dividerColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Options
                  if (isOwnComment) ...[
                    _buildBottomSheetOption(
                      context,
                      icon: Icons.edit_outlined,
                      title: 'Edit Comment',
                      onTap: () {
                        Navigator.pop(context);
                        _editComment(context, comment, activityCubit);
                      },
                    ),
                    _buildBottomSheetOption(
                      context,
                      icon: Icons.delete_outline,
                      title: 'Delete Comment',
                      isDestructive: true,
                      onTap: () {
                        Navigator.pop(context);
                        _deleteComment(context, comment, activityCubit);
                      },
                    ),
                  ] else ...[
                    _buildBottomSheetOption(
                      context,
                      icon: Icons.flag_outlined,
                      title: 'Report Comment',
                      isDestructive: true,
                      onTap: () {
                        Navigator.pop(context);
                        _reportComment(context, comment, activityCubit);
                      },
                    ),
                  ],

                  _buildBottomSheetOption(
                    context,
                    icon: Icons.copy_outlined,
                    title: 'Copy Text',
                    onTap: () {
                      Navigator.pop(context);
                      Clipboard.setData(ClipboardData(text: comment.text));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Comment copied to clipboard'),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildBottomSheetOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);
    
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : theme.colorScheme.onSurface,
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: isDestructive ? Colors.red : theme.colorScheme.onSurface,
        ),
      ),
      onTap: onTap,
    );
  }

  void _editComment(
    BuildContext context,
    ActivityComment comment,
    ActivityCubit activityCubit,
  ) {
    final editController = TextEditingController(text: comment.text);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Edit Comment'),
            content: TextField(
              controller: editController,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.editComment,
                border: OutlineInputBorder(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () {
                  if (editController.text.trim().isNotEmpty) {
                    activityCubit.editComment(
                      commentId: comment.id,
                      activityId: widget.activity.id,
                      newText: editController.text.trim(),
                    );
                    Navigator.pop(context);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  void _deleteComment(
    BuildContext context,
    ActivityComment comment,
    ActivityCubit activityCubit,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Comment'),
            content: const Text(
              'Are you sure you want to delete this comment? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              FilledButton(
                style: FilledButton.styleFrom(backgroundColor: Colors.red),
                onPressed: () {
                  activityCubit.deleteComment(
                    commentId: comment.id,
                    activityId: widget.activity.id,
                  );
                  Navigator.pop(context);
                },
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void _reportComment(
    BuildContext context,
    ActivityComment comment,
    ActivityCubit activityCubit,
  ) {
    String selectedReason = 'Inappropriate content';
    final reasons = [
      'Inappropriate content',
      'Spam',
      'Harassment',
      'False information',
      'Other',
    ];
    
    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('Report Comment'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Why are you reporting this comment?'),
                      const SizedBox(height: 16),
                      ...reasons.map(
                        (reason) => RadioListTile<String>(
                          title: Text(reason),
                          value: reason,
                          groupValue: selectedReason,
                          onChanged: (value) {
                            setState(() => selectedReason = value!);
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    FilledButton(
                      style: FilledButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                      onPressed: () {
                        activityCubit.reportComment(
                          commentId: comment.id,
                          reason: selectedReason,
                        );
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Comment reported. Thank you for your feedback.',
                            ),
                          ),
                        );
                      },
                      child: const Text('Report'),
                    ),
                  ],
                ),
          ),
    );
  }

  String _formatDetailedDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 7) {
      return '${date.day}/${date.month}/${date.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildEnhancedActionButton(
    BuildContext context,
    bool isActive,
    bool isRunning,
  ) {
    final l10n = AppLocalizations.of(context)!;
    final activityColor = AppColors.primary;

    return SizedBox(
      width: double.infinity,
      child: FloatingActionButton.extended(
        onPressed: isActive ? _cancelActivity : _startActivity,
        icon: Icon(isActive ? Icons.stop : Icons.play_arrow),
        label: Text(isActive ? l10n.stopButton : l10n.startButton),
        backgroundColor: isActive ? Colors.redAccent : activityColor,
        foregroundColor: Colors.white,
        elevation: 4,
      ),
    );
  }

  Widget _buildEnhancedFavoriteButton(
    BuildContext context,
    ActivityState state,
  ) {
    // Get the activity with updated favorite status from the state
    final activity = _getActivityWithLatestStatus(state);

    return IconButton(
      icon: Icon(
        activity.isFavorite ? Icons.favorite : Icons.favorite_border,
        color: activity.isFavorite ? Colors.red : Colors.white,
      ),
      tooltip: 'Add to favorites',
      onPressed: _toggleFavorite,
    );
  }

  Widget _buildImagePlaceholder(
    ActivityCategory category,
    ColorScheme colorScheme,
  ) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.8),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.primary.withValues(alpha: 1.0),
          ],
        ),
      ),
      child: Center(
        child: Icon(
          category.icon,
          size: 80,
          color: Colors.white.withValues(alpha: 0.8),
        ),
      ),
    );
  }

  // Calculate points based on activity duration - 1 point per minute
  int _calculatePoints(Activity activity) {
    // 1 point per minute as per requirements
    int points = activity.duration;

    // No difficulty multiplier as per requirements
    return points;
  }

  // Helper method to get the activity with updated status from global state
  Activity _getActivityWithLatestStatus(ActivityState state) {
    if (state.activities != null) {
      final updatedActivity = state.activities!.firstWhere(
        (a) => a.id == widget.activity.id,
        orElse: () => widget.activity,
      );
      return updatedActivity;
    }
    return widget.activity;
  }

  Widget _buildHealthBenefitsTags(BuildContext context) {
    final theme = Theme.of(context);

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          widget.activity.healthBenefits.map((benefit) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondaryContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                benefit,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    final theme = Theme.of(context);
    final activityColor = AppColors.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: activityColor,
          ),
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  Widget _buildMoodsList(BuildContext context) {
    final theme = Theme.of(context);

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          widget.activity.recommendedFor.map((mood) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.tertiaryContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '${mood.emoji} ${mood.localizedDescription(context)}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.black54,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }).toList(),
    );
  }
}

/// Custom painter for the love burst animation
class LoveBurstPainter extends CustomPainter {
  final double progress;

  LoveBurstPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    if (progress == 0) return;

    final paint =
        Paint()
          ..color = Colors.red.withValues(alpha: 1.0 - progress)
          ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);

    // Draw multiple hearts bursting outward
    final heartPositions = [
      Offset(-30, -30),
      Offset(30, -30),
      Offset(-50, 0),
      Offset(50, 0),
      Offset(-30, 30),
      Offset(30, 30),
      Offset(0, -50),
      Offset(0, 50),
    ];

    for (int i = 0; i < heartPositions.length; i++) {
      final offset = heartPositions[i] * progress * 2;
      final heartCenter = center + offset;

      // Scale hearts based on progress
      final scale = (1.0 - progress) * 0.8 + 0.2;

      _drawHeart(canvas, heartCenter, paint, scale);
    }
  }

  void _drawHeart(Canvas canvas, Offset center, Paint paint, double scale) {
    final size = 20.0 * scale;
    final path = Path();

    // Create heart shape
    path.moveTo(center.dx, center.dy + size * 0.3);
    path.cubicTo(
      center.dx - size * 0.5,
      center.dy - size * 0.1,
      center.dx - size * 0.5,
      center.dy - size * 0.5,
      center.dx,
      center.dy - size * 0.2,
    );
    path.cubicTo(
      center.dx + size * 0.5,
      center.dy - size * 0.5,
      center.dx + size * 0.5,
      center.dy - size * 0.1,
      center.dx,
      center.dy + size * 0.3,
    );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant LoveBurstPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

/// Custom delegate for the pinned TabBar
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;
  final Color backgroundColor;

  _SliverTabBarDelegate(this.tabBar, {required this.backgroundColor});

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
            width: 0.5,
          ),
        ),
      ),
      child: tabBar,
    );
  }

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  bool shouldRebuild(covariant _SliverTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar ||
        backgroundColor != oldDelegate.backgroundColor;
  }
}
