import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:detoxme/domain/entities/food_mood.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_state.dart';
import 'dart:math' as math;

/// Card widget for displaying a food recommendation, styled to match ActivityCard
class FoodRecommendationCard extends StatelessWidget {
  /// The food recommendation to display
  final FoodRecommendation recommendation;

  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Whether to show voting controls
  final bool showVoting;

  /// Creates a new [FoodRecommendationCard]
  const FoodRecommendationCard({
    required this.recommendation,
    required this.onTap,
    this.showVoting = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Check if recommendation was created within the last 7 days
    final isNew =
        DateTime.now().difference(recommendation.createdAt).inDays < 7;

    // Get primary color for the card
    final cardColor = _getCardColor();

    // Use GestureDetector for double tap
    return GestureDetector(
      onDoubleTap: () {
        // Double tap to upvote
        final foodMoodCubit = context.read<FoodMoodCubit>();
        foodMoodCubit.submitVote(recommendation.id, VoteType.upvote);

        // Show feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              recommendation.userVote == VoteType.upvote
                  ? 'Removed upvote from ${recommendation.name}'
                  : 'Upvoted ${recommendation.name}',
            ),
            duration: const Duration(seconds: 1),
            backgroundColor: cardColor,
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cardColor.withValues(alpha: 0.15),
              cardColor.withValues(alpha: 0.08),
              theme.colorScheme.surface.withValues(alpha: 0.95),
            ],
            stops: const [0.0, 0.4, 1.0],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: cardColor.withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: cardColor.withValues(alpha: 0.1),
              blurRadius: 12,
              spreadRadius: 2,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: theme.colorScheme.shadow.withValues(alpha: 0.05),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with image/icon and NEW badge
                SizedBox(
                  height: 100,
                  child: Stack(
                    children: [
                      // Background image or placeholder
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                cardColor.withValues(alpha: 0.3),
                                cardColor.withValues(alpha: 0.5),
                              ],
                            ),
                          ),
                          child: _buildImageContent(cardColor),
                        ),
                      ),

                      // NEW badge overlay
                      if (isNew)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  cardColor,
                                  cardColor.withValues(alpha: 0.8),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.auto_awesome,
                                  size: 12,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'NEW',
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                const SizedBox(height: 12),

                // Food name/title
                Text(
                  recommendation.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: cardColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 6),

                // Food description
                Text(
                  recommendation.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.75),
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Mood indicators with enhanced styling
                if (recommendation.moodTypes != null &&
                    recommendation.moodTypes!.isNotEmpty)
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    children:
                        recommendation.moodTypes!.take(3).map((mood) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 10,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  cardColor.withValues(alpha: 0.2),
                                  cardColor.withValues(alpha: 0.1),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: cardColor.withValues(alpha: 0.4),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  mood.emoji,
                                  style: const TextStyle(fontSize: 14),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  mood.name,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: cardColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 11,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                  ),

                const SizedBox(height: 12),

                // Enhanced vote section
                Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 12,
                  ),
                  decoration: BoxDecoration(
                    color: cardColor.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: cardColor.withValues(alpha: 0.15),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Vote count with enhanced styling
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              cardColor.withValues(alpha: 0.15),
                              cardColor.withValues(alpha: 0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.favorite, size: 16, color: cardColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${_getTotalVotes()}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: cardColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Enhanced voting controls
                      BlocBuilder<FoodMoodCubit, FoodMoodState>(
                        buildWhen:
                            (previous, current) =>
                                current.votingRecommendationId ==
                                    recommendation.id ||
                                previous.votingRecommendationId ==
                                    recommendation.id,
                        builder: (context, state) {
                          final isVoting =
                              state.isVoteInProgress &&
                              state.votingRecommendationId == recommendation.id;

                          if (isVoting) {
                            return Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: cardColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: cardColor,
                                ),
                              ),
                            );
                          }

                          return Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Upvote button
                              Container(
                                decoration: BoxDecoration(
                                  gradient:
                                      recommendation.userVote == VoteType.upvote
                                          ? LinearGradient(
                                            colors: [
                                              cardColor,
                                              cardColor.withValues(alpha: 0.8),
                                            ],
                                          )
                                          : null,
                                  color:
                                      recommendation.userVote != VoteType.upvote
                                          ? cardColor.withValues(alpha: 0.1)
                                          : null,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: cardColor.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: InkWell(
                                  onTap: () {
                                    final foodMoodCubit =
                                        context.read<FoodMoodCubit>();
                                    foodMoodCubit.submitVote(
                                      recommendation.id,
                                      VoteType.upvote,
                                    );
                                  },
                                  borderRadius: BorderRadius.circular(8),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8),
                                    child: Icon(
                                      Icons.thumb_up,
                                      size: 16,
                                      color:
                                          recommendation.userVote ==
                                                  VoteType.upvote
                                              ? Colors.white
                                              : cardColor,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),

                              // Downvote button
                              Container(
                                decoration: BoxDecoration(
                                  gradient:
                                      recommendation.userVote ==
                                              VoteType.downvote
                                          ? LinearGradient(
                                            colors: [
                                              colorScheme.error,
                                              colorScheme.error.withValues(
                                                alpha: 0.8,
                                              ),
                                            ],
                                          )
                                          : null,
                                  color:
                                      recommendation.userVote !=
                                              VoteType.downvote
                                          ? colorScheme.error.withValues(
                                            alpha: 0.1,
                                          )
                                          : null,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: colorScheme.error.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 1,
                                  ),
                                ),
                                child: InkWell(
                                  onTap: () {
                                    final foodMoodCubit =
                                        context.read<FoodMoodCubit>();
                                    foodMoodCubit.submitVote(
                                      recommendation.id,
                                      VoteType.downvote,
                                    );
                                  },
                                  borderRadius: BorderRadius.circular(8),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8),
                                    child: Icon(
                                      Icons.thumb_down,
                                      size: 16,
                                      color:
                                          recommendation.userVote ==
                                                  VoteType.downvote
                                              ? Colors.white
                                              : colorScheme.error,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Get the primary color for this card
  Color _getCardColor() {
    if (recommendation.moodTypes?.isNotEmpty == true) {
      return _getMoodColor(recommendation.moodTypes!.first);
    }
    return _getRandomColor();
  }

  /// Build the image content for the card header
  Widget _buildImageContent(Color cardColor) {
    if (recommendation.imageUrl.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: CachedNetworkImage(
          imageUrl: recommendation.imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => _buildImagePlaceholder(cardColor),
          errorWidget:
              (context, url, error) => _buildImagePlaceholder(cardColor),
        ),
      );
    }
    return _buildImagePlaceholder(cardColor);
  }

  /// Build image placeholder with enhanced styling
  Widget _buildImagePlaceholder(Color cardColor) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cardColor.withValues(alpha: 0.4),
            cardColor.withValues(alpha: 0.6),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant_menu,
              size: 36,
              color: Colors.white.withValues(alpha: 0.9),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Food',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Get total votes (upvotes - downvotes)
  int _getTotalVotes() {
    return recommendation.upvotes - recommendation.downvotes;
  }

  /// Get gradient colors based on mood or random vibrant colors
  List<Color> _getGradientColors() {
    if (recommendation.moodTypes?.isNotEmpty == true) {
      final moodColor = _getMoodColor(recommendation.moodTypes!.first);
      return [
        moodColor.withValues(alpha: 0.3),
        moodColor.withValues(alpha: 0.1),
      ];
    }

    // Choose a random vibrant color pair if no mood is available
    final randomColor = _getRandomColor();
    return [
      randomColor.withValues(alpha: 0.3),
      randomColor.withValues(alpha: 0.1),
    ];
  }

  /// Get a random color for the background
  Color _getRandomColor() {
    final colors = [
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.green,
    ];

    // Use recommendation ID hash to pick a color (consistent for same recommendation)
    final hash = recommendation.id.hashCode.abs();
    return colors[hash % colors.length];
  }

  Widget _buildImage(ColorScheme colorScheme) {
    // Use image if available, otherwise use a placeholder
    if (recommendation.imageUrl.isNotEmpty) {
      // Try to load network image with caching, show placeholder on error
      return CachedNetworkImage(
        imageUrl: recommendation.imageUrl,
        fit: BoxFit.cover,
        maxWidthDiskCache: 800, // Limit max width for disk cache
        maxHeightDiskCache: 400, // Limit max height for disk cache
        memCacheWidth: 800, // Limit memory cache width
        memCacheHeight: 400, // Limit memory cache height
        useOldImageOnUrlChange: true, // Enable persistent caching
        placeholder: (context, url) => _buildPlaceholder(colorScheme),
        errorWidget: (context, url, error) => _buildPlaceholder(colorScheme),
      );
    } else {
      return _buildPlaceholder(colorScheme);
    }
  }

  Widget _buildPlaceholder(ColorScheme colorScheme) {
    // Get appropriate mood color or random color
    final color =
        recommendation.moodTypes?.isNotEmpty == true
            ? _getMoodColor(recommendation.moodTypes!.first)
            : _getRandomColor();
    
    // Match style with ActivityCard - using color with low alpha (30) for background
    return Container(
      color: color.withValues(alpha: 0.3),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bakery_dining, // Food icon
              size: 48,
              color: color.withValues(alpha: 0.47),
            ),
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              margin: const EdgeInsets.symmetric(horizontal: 6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.25),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Recommendation',
                style: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                  fontWeight: FontWeight.bold,
                  fontSize: 11,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoodChip(MoodType mood, ThemeData theme) {
    final color = _getMoodColor(mood);
    final icon = _getMoodIcon(mood);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon, size: 14, color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              _getMoodDisplayName(mood),
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods to get mood-specific properties
  Color _getMoodColor(MoodType? mood) {
    if (mood == null) return Colors.blueGrey;

    switch (mood) {
      case MoodType.happy:
        return const Color(0xFFFF9800); // Warm orange - matches mood entity
      case MoodType.sad:
        return Colors.blue;
      case MoodType.anxious:
        return Colors.teal;
      case MoodType.angry:
        return Colors.deepOrange;
      case MoodType.tired:
        return Colors.purple;
      case MoodType.excited:
        return const Color(0xFF8E24AA); // Vibrant purple - matches mood entity
      case MoodType.calm:
        return Colors.green;
      case MoodType.neutral:
        return Colors.grey;
      case MoodType.inspired:
        return Colors.lightBlue;
      default:
        return Colors.blueGrey;
    }
  }

  IconData _getMoodIcon(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return Icons.sentiment_very_satisfied;
      case MoodType.sad:
        return Icons.sentiment_very_dissatisfied;
      case MoodType.anxious:
        return Icons.heart_broken;
      case MoodType.angry:
        return Icons.mood_bad;
      case MoodType.tired:
        return Icons.bedtime;
      case MoodType.excited:
        return Icons.celebration;
      case MoodType.calm:
        return Icons.spa;
      case MoodType.neutral:
        return Icons.sentiment_neutral;
      case MoodType.inspired:
        return Icons.lightbulb;
      default:
        return Icons.mood;
    }
  }

  String _getMoodDisplayName(MoodType mood) {
    switch (mood) {
      case MoodType.happy:
        return 'Happy';
      case MoodType.sad:
        return 'Sad';
      case MoodType.anxious:
        return 'Anxious';
      case MoodType.angry:
        return 'Angry';
      case MoodType.tired:
        return 'Tired';
      case MoodType.excited:
        return 'Excited';
      case MoodType.calm:
        return 'Calm';
      case MoodType.neutral:
        return 'Neutral';
      case MoodType.inspired:
        return 'Inspired';
      default:
        return 'Unknown';
    }
  }

  // Helper to format dates
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 1) {
      if (difference.inHours < 1) {
        return '${difference.inMinutes} min ago';
      }
      return '${difference.inHours} hrs ago';
    }

    if (difference.inDays < 30) {
      return '${difference.inDays} days ago';
    }

    // Format as MM/DD/YYYY
    return '${date.month}/${date.day}/${date.year}';
  }
}

/// Custom painter to create a food-themed pattern background
class FoodPatternPainter extends CustomPainter {
  final Color color;

  FoodPatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.5;

    // Draw some food-related shapes
    // Apple
    final appleCenter = Offset(size.width * 0.2, size.height * 0.3);
    canvas.drawCircle(appleCenter, 8, paint);
    final stemPath =
        Path()
          ..moveTo(appleCenter.dx, appleCenter.dy - 8)
          ..quadraticBezierTo(
            appleCenter.dx + 5,
            appleCenter.dy - 15,
            appleCenter.dx + 3,
            appleCenter.dy - 12,
          );
    canvas.drawPath(stemPath, paint);

    // Fork
    final forkStart = Offset(size.width * 0.7, size.height * 0.7);
    final forkPath =
        Path()
          ..moveTo(forkStart.dx, forkStart.dy)
          ..lineTo(forkStart.dx, forkStart.dy - 20)
          ..moveTo(forkStart.dx - 5, forkStart.dy - 15)
          ..lineTo(forkStart.dx - 5, forkStart.dy - 25)
          ..moveTo(forkStart.dx, forkStart.dy - 15)
          ..lineTo(forkStart.dx, forkStart.dy - 25)
          ..moveTo(forkStart.dx + 5, forkStart.dy - 15)
          ..lineTo(forkStart.dx + 5, forkStart.dy - 25);
    canvas.drawPath(forkPath, paint);

    // Plate
    final plateCenter = Offset(size.width * 0.5, size.height * 0.5);
    canvas.drawCircle(plateCenter, 15, paint);
    canvas.drawCircle(plateCenter, 12, paint);

    // Add additional decorative elements
    // Spoon
    final spoonStart = Offset(size.width * 0.3, size.height * 0.8);
    final spoonPath =
        Path()
          ..moveTo(spoonStart.dx, spoonStart.dy)
          ..lineTo(spoonStart.dx, spoonStart.dy - 15)
          ..addOval(
            Rect.fromCircle(
              center: Offset(spoonStart.dx, spoonStart.dy - 22),
              radius: 6,
            ),
          );
    canvas.drawPath(spoonPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
