import 'package:detoxme/presentation/ui/screens/dashboard/widgets/food_recommendation_card.dart';
import 'package:detoxme/presentation/ui/screens/food_mood/widgets/shimmer_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:animate_do/animate_do.dart';
import 'package:go_router/go_router.dart';
import 'package:detoxme/core/router/app_router.dart';
import 'package:detoxme/domain/entities/mood.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_cubit.dart';
import 'package:detoxme/presentation/bloc/food_mood/food_mood_state.dart';

import 'package:detoxme/localization/app_localizations.dart';

/// View for the Food Mood screen
class FoodMoodView extends StatefulWidget {
  /// Creates a new [FoodMoodView]
  const FoodMoodView({super.key});

  @override
  State<FoodMoodView> createState() => _FoodMoodViewState();
}

class _FoodMoodViewState extends State<FoodMoodView> {
  @override
  void initState() {
    super.initState();

    // Initialize with the current mood from dashboard if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final dashboardCubit = context.read<DashboardCubit>();
      final foodMoodCubit = context.read<FoodMoodCubit>();
      final currentMood = dashboardCubit.getCurrentMood();

      if (currentMood != null) {
        // Apply the selected mood from dashboard to the food filter
        foodMoodCubit.loadFoodRecommendationsForMood(
          currentMood,
          sortOption: foodMoodCubit.state.sortOption,
          excludeTooManyDownvotes: foodMoodCubit.state.excludeTooManyDownvotes,
        );
      } else {
        // If no mood is selected in dashboard, default to happy mood
        dashboardCubit.setCurrentMood(MoodType.happy);
        foodMoodCubit.loadFoodRecommendationsForMood(
          MoodType.happy,
          sortOption: foodMoodCubit.state.sortOption,
          excludeTooManyDownvotes: foodMoodCubit.state.excludeTooManyDownvotes,
        );
      }

      // Force a rebuild to ensure the mood selector is updated
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to dashboard mood changes
        BlocListener<DashboardCubit, DashboardState>(
          listenWhen:
              (previous, current) =>
                  previous.currentMood != current.currentMood,
          listener: (context, dashboardState) {
            if (dashboardState.currentMood != null) {
              // Update food recommendations when dashboard mood changes
              final foodMoodCubit = context.read<FoodMoodCubit>();
              foodMoodCubit.loadFoodRecommendationsForMood(
                dashboardState.currentMood!,
                sortOption: foodMoodCubit.state.sortOption,
                excludeTooManyDownvotes:
                    foodMoodCubit.state.excludeTooManyDownvotes,
              );
            }
          },
        ),
      ],
      child: BlocConsumer<FoodMoodCubit, FoodMoodState>(
        listener: (context, state) {
          if (state.status == FoodMoodStatus.error && state.failure != null) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.failure!.message)));
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () async {
              if (!mounted) return;

              try {
                final foodMoodCubit = context.read<FoodMoodCubit>();
                final dashboardCubit = context.read<DashboardCubit>();
                final currentMood = dashboardCubit.getCurrentMood();

                if (currentMood != null) {
                  await foodMoodCubit.loadFoodRecommendationsForMood(
                    currentMood,
                    sortOption: foodMoodCubit.state.sortOption,
                    excludeTooManyDownvotes:
                        foodMoodCubit.state.excludeTooManyDownvotes,
                  );
                } else {
                  await foodMoodCubit.loadFoodRecommendations(
                    countryCode: foodMoodCubit.state.countryCode,
                  );
                }
              } catch (e) {
                // Safe way to show error message after async operation
                if (mounted) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      final theme = Theme.of(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text(
                            "Refresh failed. Please try again.",
                          ),
                          backgroundColor: theme.colorScheme.error,
                        ),
                      );
                    }
                  });
                }
              }
            },
            child: CustomScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                // Motivational explanation section
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: FadeInUp(
                      duration: const Duration(milliseconds: 600),
                      child: _buildMotivationalMessage(context),
                    ),
                  ),
                ),

                // Did you know card
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                    child: FadeInUp(
                      duration: const Duration(milliseconds: 800),
                      delay: const Duration(milliseconds: 200),
                      child: _buildFoodTipsCard(context),
                    ),
                  ),
                ),

                // Section header
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                    child: FadeInUp(
                      duration: const Duration(milliseconds: 800),
                      delay: const Duration(milliseconds: 400),
                      child: _buildSectionHeader(context, state),
                    ),
                  ),
                ),

                // Food recommendations grid
                _buildRecommendationsGrid(context, state),

                // Bottom spacing
                const SliverToBoxAdapter(child: SizedBox(height: 120)),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMotivationalMessage(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.25),
            theme.colorScheme.primary.withValues(alpha: 0.15),
            theme.colorScheme.primary.withValues(alpha: 0.08),
            theme.colorScheme.surface.withValues(alpha: 0.95),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.4),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.primary.withValues(alpha: 0.2),
                      theme.colorScheme.primary.withValues(alpha: 0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.primary.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                ),
                child: Icon(
                  Icons.restaurant_menu,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  l10n.foodMoodExplanationTitle,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            l10n.foodMoodExplanationText,
            style: theme.textTheme.bodyMedium?.copyWith(
              height: 1.4,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.7,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.psychology,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 6),
                Text(
                  'Mood-based nutrition',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodTipsCard(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.tertiary,
            theme.colorScheme.primary.withValues(alpha: 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                'Food Tips',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Different foods can influence your mood and energy levels. Choose foods that match how you want to feel!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, FoodMoodState state) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary.withValues(alpha: 0.2),
                theme.colorScheme.primary.withValues(alpha: 0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
              width: 1.5,
            ),
          ),
          child: Icon(
            state.selectedMood != null
                ? Icons.emoji_food_beverage
                : Icons.restaurant,
            color: theme.colorScheme.primary,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            state.selectedMood != null
                ? 'Food for ${state.selectedMood!.name} mood'
                : l10n.foodRecommendations,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendationsGrid(BuildContext context, FoodMoodState state) {
    final l10n = AppLocalizations.of(context)!;

    if (state.status == FoodMoodStatus.loading) {
      return SliverPadding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
        sliver: SliverGrid(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.75,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          delegate: SliverChildBuilderDelegate(
            (context, index) => FadeInUp(
              duration: Duration(milliseconds: 600 + (index * 100)),
              child: const FoodRecommendationShimmerCard(),
            ),
            childCount: 6,
          ),
        ),
      );
    }

    final recommendations =
        state.filteredRecommendations
            .where(
              (r) =>
                  state.countryCode == null ||
                  state.countryCode!.isEmpty ||
                  r.countryCode == state.countryCode,
            )
            .toList();

    if (recommendations.isEmpty) {
      return SliverToBoxAdapter(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 32, 16, 0),
          child: FadeInUp(
            duration: const Duration(milliseconds: 800),
            child: _buildEmptyState(
              context,
              l10n.noRecommendationsFound,
              l10n.tryDifferentMood,
            ),
          ),
        ),
      );
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = screenWidth < 600 ? 2 : 3;
    final childAspectRatio = screenWidth < 600 ? 0.75 : 0.8;

    return SliverPadding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      sliver: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: childAspectRatio,
        ),
        delegate: SliverChildBuilderDelegate((context, index) {
          final recommendation = recommendations[index];
          return FadeInUp(
            delay: Duration(milliseconds: 600 + (index * 50)),
            duration: const Duration(milliseconds: 800),
            child: FoodRecommendationCard(
              recommendation: recommendation,
              showVoting: true,
              onTap: () {
                context.push(
                  AppRoutes.foodRecommendationDetails,
                  extra: recommendation,
                );
              },
            ),
          );
        }, childCount: recommendations.length),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, String title, String message) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            theme.colorScheme.surface,
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.primary.withValues(alpha: 0.15),
                  theme.colorScheme.primary.withValues(alpha: 0.05),
                ],
              ),
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.2),
                width: 2,
              ),
            ),
            child: Icon(
              Icons.no_meals_ouline,
              size: 48,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.5,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.sentiment_satisfied,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 6),
                Text(
                  'Try a different mood',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
