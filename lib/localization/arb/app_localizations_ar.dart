// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'ديتوكس مي';

  @override
  String get pinScreenSetupTitle => 'Set up Admin PIN';

  @override
  String get pinScreenEnterTitle => 'Enter Admin PIN';

  @override
  String get pinScreenSetupSubtitle => 'Set up a 4-digit PIN';

  @override
  String get pinScreenEnterSubtitle => 'Enter your 4-digit PIN';

  @override
  String get pinScreenSettingPin => 'Setting PIN...';

  @override
  String get pinScreenVerifyingPin => 'Verifying PIN...';

  @override
  String get pinScreenSaving => 'Saving...';

  @override
  String get pinScreenChecking => 'Checking...';

  @override
  String get pinScreenSetupFailed => 'PIN Setup Failed';

  @override
  String get pinScreenIncorrectPin => 'Incorrect PIN';

  @override
  String get pinScreenSuccessSet => 'PIN Set Successfully!';

  @override
  String get pinScreenSuccessVerified => 'PIN Verified!';

  @override
  String get pinScreenErrorSnackbarPrefix => 'Error: ';

  @override
  String get homeScreenTitle => 'Home';

  @override
  String get homeScreenWelcome => 'Welcome back!';

  @override
  String get homeScreenSubtitle => 'Take a moment for yourself today';

  @override
  String get profileScreenTitle => 'Profile';

  @override
  String get profileEditTitle => 'Edit Profile';

  @override
  String get profileUsernameLabel => 'Username';

  @override
  String get profileAvatarSelectionLabel => 'Choose Avatar';

  @override
  String get profileSaveButton => 'Save Profile';

  @override
  String get profileSaveSuccess => 'Profile saved successfully!';

  @override
  String get profileSaveFailure => 'Failed to save profile: ';

  @override
  String get profileLoadFailure => 'فشل في تحميل الملف الشخصي: ';

  @override
  String get profileYourProgress => 'تقدمك';

  @override
  String get profilePoints => 'النقاط';

  @override
  String get profileMyGoals => 'أهدافي';

  @override
  String get profileFavoriteActivities => 'الأنشطة المفضلة';

  @override
  String get profileNoFavoriteActivities => 'لا توجد أنشطة مفضلة حتى الآن';

  @override
  String get profileAddFavoriteActivitiesHint =>
      'ضع علامة على الأنشطة كمفضلة لرؤيتها هنا';

  @override
  String get profileExploreActivities => 'استكشاف الأنشطة';

  @override
  String get profileExploreMoreActivities => 'استكشاف المزيد من الأنشطة';

  @override
  String get profileQuickActions => 'إجراءات سريعة';

  @override
  String get profileActionActivityHistory => 'سجل الأنشطة';

  @override
  String get profileActionUsedCoupons => 'الكوبونات المستخدمة';

  @override
  String get profileActionRewardHistory => 'سجل المكافآت';

  @override
  String get profileActionSettings => 'الإعدادات';

  @override
  String get profileViewAll => 'عرض الكل';

  @override
  String homeScreenError(String error) {
    return 'خطأ في تحميل المهام: $error';
  }

  @override
  String get homeScreenNoTasks => 'No tasks available right now!';

  @override
  String homeScreenTaskDuration(String duration) {
    return 'Duration: $duration';
  }

  @override
  String get homeScreenStartButton => 'Begin Digital Detox';

  @override
  String get homeScreenAnotherTaskButton => 'Try a different challenge';

  @override
  String get detoxPointsLabel => 'Detox Points';

  @override
  String greetingMessage(String username) {
    return 'Good Day, $username!';
  }

  @override
  String get mindfulActivitySuggestions => 'Mindful Activity Suggestions';

  @override
  String get homeScreenProfileTooltip => 'Profile';

  @override
  String get timerOverlayExitTooltip => 'Exit Challenge (Requires PIN)';

  @override
  String get timerOverlayIncorrectPin => 'Incorrect PIN';

  @override
  String rewardNotificationXp(int xp) {
    return 'You earned $xp Detox Points!';
  }

  @override
  String rewardNotificationLevelUp(int levelNumber, String levelName) {
    return 'New Milestone! Reached Level $levelNumber: $levelName';
  }

  @override
  String rewardNotificationBadge(String badgeName) {
    return 'Achievement Unlocked: $badgeName';
  }

  @override
  String rewardNotificationCard(String cardName, String cardRarity) {
    return 'Reward Unlocked: $cardName ($cardRarity)';
  }

  @override
  String rewardErrorSnackbar(String errorMessage) {
    return 'Error processing rewards: $errorMessage';
  }

  @override
  String get rewardDialogTitle => 'Challenge Complete!';

  @override
  String get dialogButtonOK => 'OK';

  @override
  String get leaderboardScreenTitle => 'Leaderboard';

  @override
  String leaderboardLoadingError(String message) {
    return 'Failed to load leaderboard: $message';
  }

  @override
  String get leaderboardEmpty => 'No entries in the leaderboard yet';

  @override
  String get unknownState => 'Unknown state';

  @override
  String get onboardingButtonNext => 'Next';

  @override
  String get authLoginTitle => 'Login';

  @override
  String get authSignUpTitle => 'Sign Up';

  @override
  String get authEmailLabel => 'Email';

  @override
  String get authPasswordLabel => 'Password';

  @override
  String get authInvalidEmailError => 'Please enter a valid email';

  @override
  String get authPasswordTooShortError =>
      'Password must be at least 6 characters';

  @override
  String get authLoginButton => 'Login';

  @override
  String get authSignUpButton => 'Sign Up';

  @override
  String get authToggleToSignUp => 'Need an account? Sign Up';

  @override
  String get authToggleToLogin => 'Have an account? Login';

  @override
  String get adultOnboardingWelcomeTitle => 'Welcome to DetoxMe!';

  @override
  String get adultOnboardingWelcomeBody =>
      'Let\'s reclaim your focus and time. A few quick questions will help us personalize your journey towards a healthier digital life.';

  @override
  String get adultOnboardingExplanationTitle => 'Why These Questions?';

  @override
  String get adultOnboardingExplanationBody =>
      'Understanding a bit about you helps us tailor suggestions and challenges. Your privacy is paramount; this information stays on your device and helps personalize your experience.';

  @override
  String get adultOnboardingAgeTitle => 'About You: Age Group';

  @override
  String get adultOnboardingAgeBody =>
      'Knowing your general age helps us suggest relevant goals and content. Choose the range that fits you best.';

  @override
  String get adultOnboardingAgeOption1 => '18-25';

  @override
  String get adultOnboardingAgeOption2 => '26-35';

  @override
  String get adultOnboardingAgeOption3 => '36-45';

  @override
  String get adultOnboardingAgeOption4 => '46-55';

  @override
  String get adultOnboardingAgeOption5 => '56+';

  @override
  String get adultOnboardingGenderTitle => 'About You: Gender';

  @override
  String get adultOnboardingGenderBody =>
      'This helps us use appropriate language and understand demographic patterns (optional).';

  @override
  String get adultOnboardingGenderOptionMale => 'Male';

  @override
  String get adultOnboardingGenderOptionFemale => 'Female';

  @override
  String get adultOnboardingGenderOptionNonBinary => 'Non-binary';

  @override
  String get adultOnboardingGenderOptionPreferNotToSay => 'Prefer not to say';

  @override
  String get adultOnboardingFamilyTitle => 'About You: Family Status';

  @override
  String get adultOnboardingFamilyBody =>
      'Understanding your home life can help tailor challenges and goals (optional).';

  @override
  String get adultOnboardingFamilyOptionSingle => 'Single';

  @override
  String get adultOnboardingFamilyOptionInRelationship =>
      'In a relationship / Married';

  @override
  String get adultOnboardingFamilyOptionHaveChildren => 'Have children';

  @override
  String get adultOnboardingFamilyOptionPreferNotToSay => 'Prefer not to say';

  @override
  String get adultOnboardingGoalsTitle => 'Your Main Goals';

  @override
  String get adultOnboardingGoalsBody =>
      'What do you hope to achieve with DetoxMe? Select your primary motivations (choose up to 3).';

  @override
  String get adultOnboardingGoalReduceScreenTime =>
      'Reduce overall screen time';

  @override
  String get adultOnboardingGoalImproveFocus => 'Improve focus & concentration';

  @override
  String get adultOnboardingGoalBeMorePresent =>
      'Be more present in daily life';

  @override
  String get adultOnboardingGoalSpendMoreTimeFamily =>
      'Spend more quality time with family/friends';

  @override
  String get adultOnboardingGoalDigitalDetox => 'Perform a digital detox';

  @override
  String get adultOnboardingGoalImproveSleep => 'Improve sleep quality';

  @override
  String get adultOnboardingGoalOther => 'Other';

  @override
  String get adultOnboardingCompletionTitle => 'All Set!';

  @override
  String get adultOnboardingCompletionBody =>
      'Thank you! You\'re ready to start your journey. Let\'s build healthier digital habits together.';

  @override
  String get adultOnboardingButtonGetStarted => 'Get Started';

  @override
  String get adultOnboardingButtonBack => 'Back';

  @override
  String get requiredErrorText => 'Please make a selection';

  @override
  String get phoneInputTitle => 'Enter Phone Number';

  @override
  String get phoneInputInstructions => 'Enter your phone number to continue';

  @override
  String get phoneInputLabel => 'Phone Number';

  @override
  String get phoneInputHint => '123456789';

  @override
  String get phoneInputEmptyError => 'Please enter a phone number';

  @override
  String get phoneInputInvalidError => 'Please enter a valid phone number';

  @override
  String get phoneInputContinueButton => 'Continue';

  @override
  String get otpVerificationTitle => 'Verify Phone Number';

  @override
  String otpVerificationCodeSent(String phoneNumber) {
    return 'Code sent to $phoneNumber';
  }

  @override
  String get otpVerificationInvalidCode => 'Please enter a valid 6-digit code';

  @override
  String get otpVerificationButton => 'Verify Code';

  @override
  String get otpVerificationDidntReceive => 'Didn\'t receive the code?';

  @override
  String get otpVerificationResend => 'Resend';

  @override
  String otpVerificationResendTimer(int seconds) {
    return 'Resend in $seconds s';
  }

  @override
  String get otpVerifiedSnackbar => 'Phone number verified successfully!';

  @override
  String get continueWithPhone => 'Continue with Phone';

  @override
  String get continueWithGoogle => 'Continue with Google';

  @override
  String get continueWithApple => 'Continue with Apple';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get onboardingTellUsAboutYourself => 'Tell us about yourself';

  @override
  String get onboardingPersonalizeExperience =>
      'We\'ll use this information to personalize your detox experience';

  @override
  String get username => 'Username';

  @override
  String get enterYourUsername => 'Enter your preferred username';

  @override
  String get usernameRequired => 'Username is required';

  @override
  String get birthYear => 'Birth Year';

  @override
  String get selectYourBirthYear => 'Select your birth year';

  @override
  String get birthYearRequired => 'Birth year is required';

  @override
  String get gender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get nonBinary => 'Non-binary';

  @override
  String get preferNotToSay => 'Prefer not to say';

  @override
  String get next => 'Next';

  @override
  String get authTitle => 'Welcome Back';

  @override
  String get authSubtitle =>
      'Sign in to continue your digital wellness journey';

  @override
  String get loginWithPhone => 'Login with Phone';

  @override
  String get loginWithEmail => 'Login with Email';

  @override
  String get emailFieldLabel => 'Email';

  @override
  String get emailRequiredError => 'Email is required';

  @override
  String get emailInvalidError => 'Please enter a valid email';

  @override
  String get passwordFieldLabel => 'Password';

  @override
  String get passwordRequiredError => 'Password is required';

  @override
  String get passwordLengthError => 'Password must be at least 6 characters';

  @override
  String get noAccountQuestion => 'Don\'t have an account?';

  @override
  String get hasAccountQuestion => 'Already have an account?';

  @override
  String get signupAction => 'Sign Up';

  @override
  String get loginAction => 'Login';

  @override
  String get loginButton => 'Login';

  @override
  String get signupButton => 'Create Account';

  @override
  String get phoneVerificationHeader => 'Verify Your Phone';

  @override
  String get phoneVerificationHint =>
      'You\'ll receive a 6-digit code to verify your phone number';

  @override
  String get otpVerificationHeader => 'Enter Verification Code';

  @override
  String get otpVerificationResendCode => 'Resend Code';

  @override
  String get otpVerificationResendIn => 'Resend in';

  @override
  String get otpVerificationVerifyButton => 'Verify & Continue';

  @override
  String get selectLifeSituation => 'Select your life situation';

  @override
  String get lifeSituationIndividual => 'Individual';

  @override
  String get lifeSituationIndividualDesc =>
      'Focus on personal growth and wellbeing';

  @override
  String get lifeSituationRelationship => 'Relationship';

  @override
  String get lifeSituationRelationshipDesc =>
      'Balance screen time with your partner';

  @override
  String get lifeSituationFamily => 'Family';

  @override
  String get lifeSituationFamilyDesc =>
      'Create healthy digital habits for your family';

  @override
  String get lifeSituationWork => 'Work';

  @override
  String get lifeSituationWorkDesc =>
      'Improve productivity and reduce digital distractions';

  @override
  String get lifeSituationErrorRequired => 'Please select a life situation';

  @override
  String get lifeSituationTitle => 'Your Life Situation';

  @override
  String get lifeSituationDescription =>
      'Select the option that best describes your current situation';

  @override
  String get lifeSituationCustom => 'Custom';

  @override
  String get rewardHistoryScreenTitle => 'Reward History';

  @override
  String get rewardHistoryEmptyTitle => 'No Rewards Yet';

  @override
  String get rewardHistoryEmptyMessage =>
      'You haven\'t redeemed any rewards yet. Complete challenges to earn points and redeem rewards.';

  @override
  String get loadRedemptionsButton => 'Load Rewards';

  @override
  String get goBackButton => 'Go Back';

  @override
  String activeActivityStatus(String activityTitle) {
    return '$activityTitle (Active)';
  }

  @override
  String pausedActivityStatus(String activityTitle) {
    return '$activityTitle (Paused)';
  }

  @override
  String get activeActivityMotivation =>
      'You\'re doing super! Stay strong and finish the task to earn valuable Detox Points.';

  @override
  String activeActivityHealthBenefit(String benefit) {
    return 'This activity is good for your health: $benefit';
  }

  @override
  String get activeActivityStopConfirmTitle => 'Are you sure?';

  @override
  String get activeActivityStopConfirmMessage =>
      'You\'re so close to completing this activity! Stay strong and continue to earn valuable Detox Points.';

  @override
  String get activeActivityKeepGoingButton => 'Keep Going';

  @override
  String get activeActivityStopButton => 'Stop Activity';

  @override
  String get activeActivityPauseButton => 'Pause';

  @override
  String get activeActivityResumeButton => 'Resume';

  @override
  String get settingsTitle => 'الإعدادات';

  @override
  String get appTheme => 'مظهر التطبيق';

  @override
  String get themeSystem => 'النظام';

  @override
  String get themeLight => 'فاتح';

  @override
  String get themeDark => 'داكن';

  @override
  String get language => 'اللغة';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get enableNotifications => 'تفعيل الإشعارات';

  @override
  String get notificationsDescription =>
      'استلام إشعارات حول الأنشطة الجديدة، الكوبونات والتذكيرات';

  @override
  String get notifyAboutActivities => 'إشعارات الأنشطة';

  @override
  String get activityMoodPreferences => 'تفضيلات المزاج';

  @override
  String get otherNotifications => 'إشعارات أخرى';

  @override
  String get newActivitiesPushNotif => 'أنشطة جديدة';

  @override
  String get newCouponsPushNotif => 'كوبونات جديدة';

  @override
  String get activityRemindersPushNotif => 'تذكيرات';

  @override
  String get motivationalMessagesPushNotif => 'رسائل تحفيزية';

  @override
  String get commentLikesPushNotif => 'إعجابات التعليقات';

  @override
  String get noNotifications => 'لا توجد إشعارات';

  @override
  String get noNotificationsDescription => 'ليس لديك أي إشعارات حتى الآن';

  @override
  String get notificationDeleted => 'تم حذف الإشعار';

  @override
  String get notificationMarkedRead => 'تم تحديده كمقروء';

  @override
  String get notificationMarkedUnread => 'تم تحديده كغير مقروء';

  @override
  String get undo => 'تراجع';

  @override
  String get activitiesScreenTitle => 'Mood & Activities';

  @override
  String get filterAndSort => 'Filter & Sort';

  @override
  String get activities => 'Activities';

  @override
  String get allActivities => 'All Activities';

  @override
  String get myActivities => 'My Activities';

  @override
  String get newest => 'Newest';

  @override
  String get mostPopular => 'Most Popular';

  @override
  String get mostUpvoted => 'Most Upvoted';

  @override
  String get highestRated => 'Highest Rated';

  @override
  String get languages => 'Languages';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String errorLoadingData(String error) {
    return 'Error loading data: $error';
  }

  @override
  String errorRefreshingData(String error) {
    return 'Error refreshing data: $error';
  }

  @override
  String get orWithEmail => 'or with email';

  @override
  String get takeDeepBreath => 'Take a deep breath';

  @override
  String get dashboardTitle => 'Dashboard';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get dailyProgress => 'Daily Progress';

  @override
  String get failedToLoadProgressData => 'Failed to load progress data';

  @override
  String get connectionProblem =>
      'There was a problem connecting to the server.';

  @override
  String get noMotivationalContent => 'No motivational content available';

  @override
  String get dailyPoints => 'daily points';

  @override
  String get foodMoodTitle => 'Food Mood';

  @override
  String get filter => 'Filter';

  @override
  String get sortBy => 'Sort By';

  @override
  String get defaultOption => 'Default';

  @override
  String get topRated => 'Top Rated';

  @override
  String get newestFirst => 'Newest First';

  @override
  String get oldestFirst => 'Oldest First';

  @override
  String get filterOptions => 'Filter Options';

  @override
  String get clearAll => 'مسح الكل';

  @override
  String get foodCategories => 'فئات الطعام';

  @override
  String get hideHighlyDownvotedItems => 'Hide Highly Downvoted Items';

  @override
  String get hideItemsWithTooManyNegativeVotes =>
      'Hide items with too many negative votes';

  @override
  String get sort => 'فرز';

  @override
  String get loadingYourProfile => 'Loading your profile...';

  @override
  String get failedToLoadProfile => 'Failed to load profile';

  @override
  String get startMyDetoxJourney => 'Start My Detox Journey';

  @override
  String get startBeingPresent => 'Start Being Present';

  @override
  String get startMyDetox => 'Start My Detox';

  @override
  String get getStarted => 'Get Started';

  @override
  String get comingSoon => 'قريباً';

  @override
  String get detoxRewards => 'مكافآت ديتوكس';

  @override
  String get newLabel => 'جديد';

  @override
  String get rewardsDescription =>
      'أكمل الأنشطة لكسب النقاط واستبدالها بمكافآت حقيقية';

  @override
  String get goodMorning => 'صباح الخير';

  @override
  String get goodAfternoon => 'مساء الخير';

  @override
  String get goodEvening => 'مساء الخير';

  @override
  String get goodNight => 'تصبح على خير';

  @override
  String get currentPointsLabel => 'الحالية';

  @override
  String get targetPointsLabel => 'الهدف';

  @override
  String get maxPointsLabel => 'الحد الأقصى';

  @override
  String get detoxActivities => 'أنشطة ديتوكس';

  @override
  String get defaultActivitiesDescription =>
      'جرب هذه الأنشطة لتحسين رفاهيتك الرقمية وكسب النقاط';

  @override
  String activitiesForMood(String mood) {
    return 'أنشطة مخصصة لمزاجك $mood';
  }

  @override
  String get beginnerActivities => 'مبتدئ';

  @override
  String get intermediateActivities => 'متوسط';

  @override
  String get expertActivities => 'خبير';

  @override
  String get loadingDashboard =>
      'جاري تحميل لوحة المعلومات الشخصية الخاصة بك...';

  @override
  String get noActivitiesAvailable =>
      'لا توجد أنشطة متاحة لهذا المزاج حتى الآن';

  @override
  String get activityInProgressError => 'لديك بالفعل نشاط قيد التقدم';

  @override
  String get startActivity => 'ابدأ';

  @override
  String get moodOMeterTitle => 'كيف تشعر اليوم؟';

  @override
  String get moodOMeterInstructions =>
      'اسحب حول مقياس المزاج أو انقر على رمز تعبيري لتحديد مزاجك الحالي';

  @override
  String get createActivityTitle => 'إنشاء نشاط';

  @override
  String get titleLabel => 'العنوان';

  @override
  String get titleHint => 'أدخل عنوانًا لنشاطك';

  @override
  String get titleRequired => 'يرجى إدخال عنوان';

  @override
  String get descriptionLabel => 'الوصف';

  @override
  String get descriptionHint => 'صف نشاطك';

  @override
  String get descriptionRequired => 'يرجى إدخال وصف';

  @override
  String durationLabel(int duration) {
    return 'المدة: $duration دقيقة';
  }

  @override
  String durationMinutes(int minutes) {
    return '$minutes دقيقة';
  }

  @override
  String get healthBenefitsLabel => 'الفوائد الصحية';

  @override
  String get healthBenefitsHint => 'وصف الفوائد الصحية لهذا النشاط';

  @override
  String get healthBenefitsRequired => 'يرجى وصف الفوائد الصحية';

  @override
  String get healthBenefitsDialogTitle => 'الفوائد الصحية';

  @override
  String get addNewHealthBenefitHint => 'إضافة فائدة صحية جديدة';

  @override
  String get selectedBenefitsLabel => 'الفوائد المحددة:';

  @override
  String get suggestedBenefitsLabel => 'الفوائد المقترحة:';

  @override
  String get categoryLabel => 'الفئة';

  @override
  String get languageLabel => 'اللغة';

  @override
  String get selectLanguageHint => 'اختر اللغة';

  @override
  String get recommendedForMoodsLabel => 'موصى به للحالات المزاجية';

  @override
  String get selectAtLeastOneMood => '(حدد واحدة على الأقل)';

  @override
  String get moodSelectionError => 'يرجى تحديد مزاج واحد على الأقل';

  @override
  String get healthBenefitsSelectionError =>
      'يرجى تحديد فائدتين صحيتين على الأقل';

  @override
  String get privacySettingsLabel => 'إعدادات الخصوصية';

  @override
  String get makeActivityPrivateLabel => 'جعل النشاط خاصًا';

  @override
  String get privateActivityDescription => 'يمكنك فقط رؤية هذا النشاط';

  @override
  String get showYourNameLabel => 'إظهار اسمك';

  @override
  String get showNameDescription => 'سيتم عرض اسمك كمنشئ';

  @override
  String get createActivityButton => 'إنشاء نشاط';

  @override
  String get activityCreatedSuccess => 'تم إنشاء النشاط بنجاح';

  @override
  String get loginRequiredError => 'يجب أن تكون مسجلاً لإنشاء الأنشطة';

  @override
  String createActivityError(String error) {
    return 'فشل إنشاء النشاط: $error';
  }

  @override
  String get selectYourMood => 'حدد مزاجك';

  @override
  String get commonMoods => 'الحالات المزاجية الشائعة:';

  @override
  String get allMoodOptions => 'جميع خيارات المزاج:';

  @override
  String get moreMoodsButton => 'المزيد';

  @override
  String get lessMoodsButton => 'أقل';

  @override
  String get noMoodSelected => 'لم يتم تحديد المزاج';

  @override
  String get selectMoodAbove => 'حدد مزاجًا أعلاه';

  @override
  String get moodSelectionDescription =>
      'سنقترح أنشطة لمساعدتك على الشعور بتحسن أو البقاء إيجابيًا';

  @override
  String get failedToLoadActivities => 'فشل تحميل الأنشطة';

  @override
  String get noActivitiesFound => 'لم يتم العثور على أنشطة';

  @override
  String get noActivitiesForMoodDescription =>
      'لم نتمكن من العثور على أنشطة لمزاجك الحالي. جرب تحديد مزاج مختلف.';

  @override
  String get noUserActivities => 'لا توجد أنشطة أنشأتها أنت';

  @override
  String get noUserActivitiesDescription =>
      'لم تنشئ أي أنشطة لهذا المزاج حتى الآن. أنشئ واحدًا أو قم بإيقاف تشغيل الفلتر.';

  @override
  String get showAllActivities => 'عرض جميع الأنشطة';

  @override
  String suggestionsForMood(String mood, String emoji) {
    return 'اقتراحات لـ $mood $emoji';
  }

  @override
  String get startButton => 'بدء النشاط';

  @override
  String get stopButton => 'إيقاف النشاط';

  @override
  String activityStarted(String activityTitle) {
    return 'Started: $activityTitle';
  }

  @override
  String activityStartError(String error) {
    return 'Failed to start activity: $error';
  }

  @override
  String get activityCancelled => 'تم إلغاء النشاط. لم يتم الحصول على نقاط.';

  @override
  String activityCancelError(String error) {
    return 'خطأ في إلغاء النشاط: $error';
  }

  @override
  String get completeCurrentActivityFirst => 'أكمل أو ألغِ النشاط الحالي أولاً';

  @override
  String get cancelButton => 'إلغاء';

  @override
  String get applyButton => 'تطبيق';

  @override
  String get healthBenefitStress => 'يقلل التوتر والقلق';

  @override
  String get healthBenefitCardio => 'يحسن صحة القلب والأوعية الدموية';

  @override
  String get healthBenefitClarity => 'يعزز وضوح الذهن';

  @override
  String get healthBenefitImmune => 'يعزز جهاز المناعة';

  @override
  String get healthBenefitSleep => 'يحسن جودة النوم';

  @override
  String get healthBenefitStrength => 'يزيد القوة البدنية';

  @override
  String get healthBenefitMindful => 'يعزز اليقظة الذهنية';

  @override
  String get healthBenefitDigital => 'يقلل الإدمان الرقمي';

  @override
  String get healthBenefitFocus => 'يحسن التركيز والانتباه';

  @override
  String get healthBenefitEmotional => 'يعزز الصحة العاطفية';

  @override
  String get healthBenefitEnergy => 'يزيد مستويات الطاقة';

  @override
  String get healthBenefitCreativity => 'يعزز الإبداع';

  @override
  String get healthBenefitPosture => 'يحسن وضعية الجسم';

  @override
  String get healthBenefitWeight => 'يساعد في إدارة الوزن';

  @override
  String get foodMoodExplanationTitle => 'هل تعلم؟';

  @override
  String get foodMoodExplanationText =>
      'الطعام الذي تتناوله يمكن أن يؤثر بشكل كبير على مزاجك وصحتك النفسية. الأطعمة الغنية بالعناصر الغذائية تدعم وظائف الدماغ وتساعد في تنظيم العواطف، بينما قد تساهم الأطعمة المعالجة في تقلبات المزاج.';

  @override
  String get noRecommendationsFound => 'لم يتم العثور على توصيات';

  @override
  String get tryDifferentMood => 'حاول اختيار مزاج مختلف أو تعديل المرشحات.';

  @override
  String get description => 'الوصف';

  @override
  String get benefits => 'الفوائد';

  @override
  String get categories => 'الفئات';

  @override
  String get hideDownvotedItems =>
      'إخفاء العناصر ذات التقييمات السلبية العالية';

  @override
  String get hideDownvotedDescription =>
      'إخفاء العناصر ذات الكثير من التقييمات السلبية';

  @override
  String get recommendedForMoods => 'موصى به للحالات المزاجية';

  @override
  String get nutritionalInfo => 'Nutritional Information';

  @override
  String get ingredients => 'المكونات';

  @override
  String get preparationSteps => 'خطوات التحضير';

  @override
  String get healthyFood => 'طعام صحي';

  @override
  String get moodHappy => 'سعيد';

  @override
  String get moodSad => 'حزين';

  @override
  String get moodAngry => 'غاضب';

  @override
  String get moodAnxious => 'قلق';

  @override
  String get moodTired => 'متعب';

  @override
  String get moodStressed => 'مضغوط';

  @override
  String get moodDepressed => 'مكتئب';

  @override
  String get moodExcited => 'متحمس';

  @override
  String get moodCalm => 'هادئ';

  @override
  String get moodBored => 'ضجر';

  @override
  String get foodCategoryProtein => 'غني بالبروتين';

  @override
  String get foodCategoryHealthyFats => 'دهون صحية';

  @override
  String get foodCategoryComplexCarbs => 'كربوهيدرات معقدة';

  @override
  String get foodCategoryVitamins => 'غني بالفيتامينات';

  @override
  String get foodCategoryMinerals => 'معادن';

  @override
  String get foodCategoryOmega3 => 'أوميغا-3';

  @override
  String get foodCategoryProbiotics => 'بروبيوتيك';

  @override
  String get foodCategoryCalming => 'مهدئ';

  @override
  String get foodCategoryEnergyBoosting => 'معزز للطاقة';

  @override
  String get foodCategoryAntioxidants => 'مضادات الأكسدة';

  @override
  String get activitiesTitle => 'الأنشطة';

  @override
  String helloUser(String userName) {
    return 'مرحبًا، $userName';
  }

  @override
  String goalTag(String goal) {
    return '$goal';
  }

  @override
  String get familyTag => 'العائلة';

  @override
  String get childrenTag => 'الأطفال';

  @override
  String get motivationChildrenFocus =>
      'أنت اليوم قدوة رائعة لأطفالك! تركيزك يظهر لهم قوة الحضور والانتباه.';

  @override
  String get motivationChildrenSleep =>
      'النوم الأفضل يعني حضورًا أكبر لعائلتك. استمر في وضع حدود رقمية!';

  @override
  String get motivationChildrenGeneric =>
      'أطفالك يلاحظون عندما تضع هاتفك جانبًا. كل لحظة تواصل معهم مهمة!';

  @override
  String get motivationFocusHigh =>
      'تركيز مذهل اليوم! ذهنك صافٍ وإنتاجيتك في ازدياد. استمر!';

  @override
  String get motivationFocusLow =>
      'كل لحظة بعيدًا عن الشاشة تساعد عقلك على إعادة التركيز. أنت قادر على ذلك!';

  @override
  String get motivationSleep =>
      'جودة نومك تتحسن مع كل استراحة رقمية. جسدك وعقلك يشكرانك!';

  @override
  String get motivationGeneric =>
      'الخطوات الصغيرة تؤدي إلى تغييرات كبيرة. أنت تعيد برمجة عقلك مع كل استراحة رقمية. واصل التقدم!';

  @override
  String get sortNotifications => 'فرز الإشعارات';

  @override
  String get byDateNewestFirst => 'حسب التاريخ (الأحدث أولاً)';

  @override
  String get unreadFirst => 'غير المقروءة أولاً';

  @override
  String unreadCount(int count) {
    return '$count غير مقروءة';
  }

  @override
  String get today => 'اليوم';

  @override
  String get yesterday => 'الأمس';

  @override
  String get delete => 'حذف';

  @override
  String get markRead => 'تحديد كمقروء';

  @override
  String get markUnread => 'تحديد كغير مقروء';

  @override
  String get couponDetails => 'Coupon Details';

  @override
  String get aboutPartner => 'عن الشريك';

  @override
  String get address => 'العنوان';

  @override
  String get openInMaps => 'فتح في الخرائط';

  @override
  String get howToUse => 'كيفية الاستخدام';

  @override
  String get termsAndConditions => 'الشروط والأحكام';

  @override
  String get validUntil => 'صالح حتى';

  @override
  String get redeemCoupon => 'استبدال القسيمة';

  @override
  String redeemFor(int points) {
    return 'استبدال مقابل $points نقطة';
  }

  @override
  String redeemConfirmation(String discount, String partner, int points) {
    return 'هل ترغب في استخدام قسيمة خصم $discount من $partner؟ سيكلفك ذلك $points نقطة.';
  }

  @override
  String get cancel => 'إلغاء';

  @override
  String get redeem => 'استبدال';

  @override
  String get openingInMaps => 'فتح في الخرائط...';

  @override
  String get shareFeatureComingSoon => 'ميزة المشاركة قادمة قريبًا!';

  @override
  String get activityDetails => 'تفاصيل النشاط';

  @override
  String get activityCompleted => 'Activity Completed!';

  @override
  String activityCompletedMessage(String title) {
    return 'Congratulations! You completed \"$title\" and earned points!';
  }

  @override
  String get ok => 'OK';

  @override
  String get addToFavorites => 'Add to favorites';

  @override
  String get removedFromFavorites => 'تمت الإزالة من المفضلة';

  @override
  String get addedToFavorites => 'تمت الإضافة إلى المفضلة';

  @override
  String get commentAdded => 'تمت إضافة تعليق';

  @override
  String get activityInProgress => 'Activity in Progress';

  @override
  String get remaining => 'remaining';

  @override
  String get elapsed => 'elapsed';

  @override
  String get stayFocused => 'Stay focused to earn more Detox Points!';

  @override
  String get activityPoints => 'Activity Points';

  @override
  String get points => 'نقاط';

  @override
  String get completeToEarn => 'Complete this activity to earn points!';

  @override
  String get aboutThisActivity => 'About this activity';

  @override
  String get healthBenefits => 'Health Benefits';

  @override
  String get noHealthBenefitsListed =>
      'No health benefits listed for this activity.';

  @override
  String get recommendedFor => 'Recommended for';

  @override
  String get noCommentsYet => 'لا توجد تعليقات بعد';

  @override
  String get beFirstToComment => 'كن أول من يشارك تجربته';

  @override
  String get shareYourExperience => 'شارك تجربتك...';

  @override
  String get justNow => 'الآن';

  @override
  String minutesAgo(int minutes) {
    return 'منذ $minutes دقيقة';
  }

  @override
  String hoursAgo(int hours) {
    return 'منذ $hours ساعة';
  }

  @override
  String daysAgo(int days) {
    return 'منذ $days يوم';
  }

  @override
  String get activityComplete => 'اكتمل النشاط';

  @override
  String get comments => 'التعليقات';

  @override
  String createdBy(String name) {
    return 'تم إنشاؤه بواسطة: $name';
  }

  @override
  String get detoxBenefits => 'فوائد التخلص من السموم';

  @override
  String get targetedMoods => 'الحالات المزاجية المستهدفة';

  @override
  String started(String name) {
    return 'بدأت: $name';
  }

  @override
  String failedToStartActivity(String error) {
    return 'فشل بدء النشاط: $error';
  }

  @override
  String pointsValue(int value) {
    return '$value نقطة';
  }

  @override
  String get upvotes => 'الإعجابات';

  @override
  String get downvotes => 'عدم الإعجاب';

  @override
  String get nutritionInfo => 'المعلومات الغذائية';

  @override
  String get calories => 'السعرات الحرارية';

  @override
  String get protein => 'البروتين';

  @override
  String get carbs => 'الكربوهيدرات';

  @override
  String get fats => 'الدهون';

  @override
  String get moodFilter => 'Filter by Mood';

  @override
  String get networkStatusOnline => 'Online';

  @override
  String get networkStatusOffline => 'Offline';

  @override
  String get networkStatusChecking => 'Checking network...';

  @override
  String get commonLoading => 'Loading...';

  @override
  String get foodImageLoadErrorTitle => 'Image Not Available';

  @override
  String get foodImageLoadErrorMessage =>
      'Could not load image. Please try again later.';

  @override
  String get suggestionsFor => 'Suggestions for';

  @override
  String get tipsAndTricks => 'Tips & Tricks';

  @override
  String get activityTips =>
      'Regular activity breaks can boost your productivity and mental wellbeing. Try to engage in screen-free activities for at least 15 minutes every hour.';

  @override
  String get loading => 'Loading...';

  @override
  String get foodRecommendations => 'Food Recommendations';

  @override
  String get timeToDisconnect => 'Time to Disconnect';

  @override
  String get activitiesMotivationalMessage =>
      'Put your phone down and dive into real life! Every activity you complete brings you closer to breaking free from digital addiction while earning valuable Detox Points.';

  @override
  String get earnPointsForActivities =>
      'Earn points for every activity completed';

  @override
  String get details => 'التفاصيل';

  @override
  String get discussion => 'المناقشة';

  @override
  String get editComment => 'عدّل تعليقك...';

  @override
  String get commentLikeNotification => 'إشعارات إعجاب التعليق';

  @override
  String get commentLikeNotificationDescription =>
      'احصل على إشعار عندما يُعجب أحدهم بتعليقك';
}
