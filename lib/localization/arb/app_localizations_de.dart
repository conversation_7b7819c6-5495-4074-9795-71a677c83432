// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appTitle => 'DetoxMe';

  @override
  String get pinScreenSetupTitle => 'Admin-PIN einrichten';

  @override
  String get pinScreenEnterTitle => 'Admin-PIN eingeben';

  @override
  String get pinScreenSetupSubtitle => 'Richte eine 4-stellige PIN ein';

  @override
  String get pinScreenEnterSubtitle => 'Gib deine 4-stellige PIN ein';

  @override
  String get pinScreenSettingPin => 'PIN wird gesetzt...';

  @override
  String get pinScreenVerifyingPin => 'PIN wird geprüft...';

  @override
  String get pinScreenSaving => 'Speichern...';

  @override
  String get pinScreenChecking => 'Prüfen...';

  @override
  String get pinScreenSetupFailed => 'PIN-Einrichtung fehlgeschlagen';

  @override
  String get pinScreenIncorrectPin => 'Falsche PIN';

  @override
  String get pinScreenSuccessSet => 'PIN erfolgreich gesetzt!';

  @override
  String get pinScreenSuccessVerified => 'PIN bestätigt!';

  @override
  String get pinScreenErrorSnackbarPrefix => 'Fehler: ';

  @override
  String get homeScreenTitle => 'Startseite';

  @override
  String get homeScreenWelcome => 'Willkommen zurück!';

  @override
  String get homeScreenSubtitle => 'Take a moment for yourself today';

  @override
  String get profileScreenTitle => 'Dein Profil';

  @override
  String get profileEditTitle => 'Profil Bearbeiten';

  @override
  String get profileUsernameLabel => 'Benutzername';

  @override
  String get profileAvatarSelectionLabel => 'Avatar wählen';

  @override
  String get profileSaveButton => 'Profil speichern';

  @override
  String get profileSaveSuccess => 'Profil erfolgreich gespeichert!';

  @override
  String get profileSaveFailure => 'Profil konnte nicht gespeichert werden: ';

  @override
  String get profileLoadFailure => 'Fehler beim Laden des Profils: ';

  @override
  String get profileYourProgress => 'Dein Fortschritt';

  @override
  String get profilePoints => 'Punkte';

  @override
  String get profileMyGoals => 'Meine Ziele';

  @override
  String get profileFavoriteActivities => 'Lieblingsaktivitäten';

  @override
  String get profileNoFavoriteActivities => 'Noch keine Lieblingsaktivitäten';

  @override
  String get profileAddFavoriteActivitiesHint =>
      'Markiere Aktivitäten als Favoriten, um sie hier zu sehen';

  @override
  String get profileExploreActivities => 'Aktivitäten erkunden';

  @override
  String get profileExploreMoreActivities => 'Mehr Aktivitäten erkunden';

  @override
  String get profileQuickActions => 'Schnellaktionen';

  @override
  String get profileActionActivityHistory => 'Aktivitätsverlauf';

  @override
  String get profileActionUsedCoupons => 'Verwendete Gutscheine';

  @override
  String get profileActionRewardHistory => 'Belohnungsverlauf';

  @override
  String get profileActionSettings => 'Einstellungen';

  @override
  String get profileViewAll => 'Alle anzeigen';

  @override
  String homeScreenError(String error) {
    return 'Fehler beim Laden der Aufgaben: $error';
  }

  @override
  String get homeScreenNoTasks => 'Momentan keine Aufgaben verfügbar!';

  @override
  String homeScreenTaskDuration(String duration) {
    return 'Dauer: $duration';
  }

  @override
  String get homeScreenStartButton => 'Digital Detox beginnen';

  @override
  String get homeScreenAnotherTaskButton => 'Andere Herausforderung versuchen';

  @override
  String get detoxPointsLabel => 'Detox Points';

  @override
  String greetingMessage(String username) {
    return 'Good Day, $username!';
  }

  @override
  String get mindfulActivitySuggestions => 'Mindful Activity Suggestions';

  @override
  String get homeScreenProfileTooltip => 'Profil';

  @override
  String get timerOverlayExitTooltip =>
      'Herausforderung verlassen (PIN benötigt)';

  @override
  String get timerOverlayIncorrectPin => 'Falsche PIN';

  @override
  String rewardNotificationXp(int xp) {
    return 'Du hast $xp Detox-Punkte verdient!';
  }

  @override
  String rewardNotificationLevelUp(int levelNumber, String levelName) {
    return 'Neuer Meilenstein! Level $levelNumber erreicht: $levelName';
  }

  @override
  String rewardNotificationBadge(String badgeName) {
    return 'Erfolg freigeschaltet: $badgeName';
  }

  @override
  String rewardNotificationCard(String cardName, String cardRarity) {
    return 'Belohnung freigeschaltet: $cardName ($cardRarity)';
  }

  @override
  String rewardErrorSnackbar(String errorMessage) {
    return 'Fehler beim Verarbeiten der Belohnungen: $errorMessage';
  }

  @override
  String get rewardDialogTitle => 'Herausforderung abgeschlossen!';

  @override
  String get dialogButtonOK => 'OK';

  @override
  String get leaderboardScreenTitle => 'Community-Erfolge';

  @override
  String leaderboardLoadingError(String message) {
    return 'Community-Daten konnten nicht geladen werden:';
  }

  @override
  String get leaderboardEmpty => 'Noch keine Community-Erfolge. Sei der Erste!';

  @override
  String get unknownState => 'Unknown state';

  @override
  String get onboardingButtonNext => 'Weiter';

  @override
  String get authLoginTitle => 'Anmelden';

  @override
  String get authSignUpTitle => 'Registrieren';

  @override
  String get authEmailLabel => 'E-Mail';

  @override
  String get authPasswordLabel => 'Passwort';

  @override
  String get authInvalidEmailError => 'Bitte gib eine gültige E-Mail ein';

  @override
  String get authPasswordTooShortError =>
      'Passwort muss mindestens 6 Zeichen lang sein';

  @override
  String get authLoginButton => 'Anmelden';

  @override
  String get authSignUpButton => 'Registrieren';

  @override
  String get authToggleToSignUp => 'Noch kein Konto? Registrieren';

  @override
  String get authToggleToLogin => 'Schon ein Konto? Anmelden';

  @override
  String get adultOnboardingWelcomeTitle => 'Willkommen bei DetoxMe!';

  @override
  String get adultOnboardingWelcomeBody =>
      'Gewinnen wir gemeinsam Fokus und Zeit zurück. Ein paar schnelle Fragen helfen uns, deine Reise zu einem gesünderen digitalen Leben zu personalisieren.';

  @override
  String get adultOnboardingExplanationTitle => 'Warum diese Fragen?';

  @override
  String get adultOnboardingExplanationBody =>
      'Ein wenig über dich zu erfahren, hilft uns, Vorschläge und Herausforderungen anzupassen. Deine Privatsphäre ist uns wichtig; diese Informationen bleiben auf deinem Gerät und helfen, deine Erfahrung zu personalisieren.';

  @override
  String get adultOnboardingAgeTitle => 'Über Dich: Altersgruppe';

  @override
  String get adultOnboardingAgeBody =>
      'Deine ungefähre Altersgruppe hilft uns, relevante Ziele und Inhalte vorzuschlagen. Wähle den Bereich, der am besten passt.';

  @override
  String get adultOnboardingAgeOption1 => '18-25';

  @override
  String get adultOnboardingAgeOption2 => '26-35';

  @override
  String get adultOnboardingAgeOption3 => '36-45';

  @override
  String get adultOnboardingAgeOption4 => '46-55';

  @override
  String get adultOnboardingAgeOption5 => '56+';

  @override
  String get adultOnboardingGenderTitle => 'Über Dich: Geschlecht';

  @override
  String get adultOnboardingGenderBody =>
      'Dies hilft uns, eine passende Sprache zu verwenden und demografische Muster zu verstehen (optional).';

  @override
  String get adultOnboardingGenderOptionMale => 'Männlich';

  @override
  String get adultOnboardingGenderOptionFemale => 'Weiblich';

  @override
  String get adultOnboardingGenderOptionNonBinary => 'Nicht-binär';

  @override
  String get adultOnboardingGenderOptionPreferNotToSay => 'Keine Angabe';

  @override
  String get adultOnboardingFamilyTitle => 'Über Dich: Familienstatus';

  @override
  String get adultOnboardingFamilyBody =>
      'Dein Familienleben zu verstehen, kann helfen, Herausforderungen und Ziele anzupassen (optional).';

  @override
  String get adultOnboardingFamilyOptionSingle => 'Single';

  @override
  String get adultOnboardingFamilyOptionInRelationship =>
      'In Beziehung / Verheiratet';

  @override
  String get adultOnboardingFamilyOptionHaveChildren => 'Hat Kinder';

  @override
  String get adultOnboardingFamilyOptionPreferNotToSay => 'Keine Angabe';

  @override
  String get adultOnboardingGoalsTitle => 'Deine Hauptziele';

  @override
  String get adultOnboardingGoalsBody =>
      'Was erhoffst du dir von DetoxMe? Wähle deine Hauptmotivationen (bis zu 3).';

  @override
  String get adultOnboardingGoalReduceScreenTime =>
      'Gesamte Bildschirmzeit reduzieren';

  @override
  String get adultOnboardingGoalImproveFocus =>
      'Fokus & Konzentration verbessern';

  @override
  String get adultOnboardingGoalBeMorePresent => 'Im Alltag präsenter sein';

  @override
  String get adultOnboardingGoalSpendMoreTimeFamily =>
      'Mehr Qualitätszeit mit Familie/Freunden verbringen';

  @override
  String get adultOnboardingGoalDigitalDetox =>
      'Einen digitalen Detox durchführen';

  @override
  String get adultOnboardingGoalImproveSleep => 'Schlafqualität verbessern';

  @override
  String get adultOnboardingGoalOther => 'Andere';

  @override
  String get adultOnboardingCompletionTitle => 'Fertig!';

  @override
  String get adultOnboardingCompletionBody =>
      'Danke! Du bist bereit, deine Reise zu beginnen. Lass uns gemeinsam gesündere digitale Gewohnheiten entwickeln.';

  @override
  String get adultOnboardingButtonGetStarted => 'Loslegen';

  @override
  String get adultOnboardingButtonBack => 'Zurück';

  @override
  String get requiredErrorText => 'Bitte triff eine Auswahl';

  @override
  String get phoneInputTitle => 'Telefonnummer eingeben';

  @override
  String get phoneInputInstructions =>
      'Gib deine Telefonnummer ein, um fortzufahren';

  @override
  String get phoneInputLabel => 'Telefonnummer';

  @override
  String get phoneInputHint => '123456789';

  @override
  String get phoneInputEmptyError => 'Bitte gib eine Telefonnummer ein';

  @override
  String get phoneInputInvalidError =>
      'Bitte gib eine gültige Telefonnummer ein';

  @override
  String get phoneInputContinueButton => 'Weiter';

  @override
  String get otpVerificationTitle => 'Telefonnummer verifizieren';

  @override
  String otpVerificationCodeSent(String phoneNumber) {
    return 'Code gesendet an $phoneNumber';
  }

  @override
  String get otpVerificationInvalidCode =>
      'Bitte gib einen gültigen 6-stelligen Code ein';

  @override
  String get otpVerificationButton => 'Code verifizieren';

  @override
  String get otpVerificationDidntReceive => 'Keinen Code erhalten?';

  @override
  String get otpVerificationResend => 'Erneut senden';

  @override
  String otpVerificationResendTimer(int seconds) {
    return 'Erneut senden in $seconds s';
  }

  @override
  String get otpVerifiedSnackbar => 'Telefonnummer erfolgreich verifiziert!';

  @override
  String get continueWithPhone => 'Mit Telefon fortfahren';

  @override
  String get continueWithGoogle => 'Mit Google fortfahren';

  @override
  String get continueWithApple => 'Mit Apple fortfahren';

  @override
  String get orContinueWith => 'Oder fortfahren mit';

  @override
  String get onboardingTellUsAboutYourself => 'Tell us about yourself';

  @override
  String get onboardingPersonalizeExperience =>
      'We\'ll use this information to personalize your detox experience';

  @override
  String get username => 'Username';

  @override
  String get enterYourUsername => 'Enter your preferred username';

  @override
  String get usernameRequired => 'Username is required';

  @override
  String get birthYear => 'Birth Year';

  @override
  String get selectYourBirthYear => 'Select your birth year';

  @override
  String get birthYearRequired => 'Birth year is required';

  @override
  String get gender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get nonBinary => 'Non-binary';

  @override
  String get preferNotToSay => 'Prefer not to say';

  @override
  String get next => 'Next';

  @override
  String get authTitle => 'Willkommen zurück';

  @override
  String get authSubtitle =>
      'Melde dich an, um deine digitale Wellness-Reise fortzusetzen';

  @override
  String get loginWithPhone => 'Mit Telefon anmelden';

  @override
  String get loginWithEmail => 'Mit E-Mail anmelden';

  @override
  String get emailFieldLabel => 'E-Mail';

  @override
  String get emailRequiredError => 'E-Mail ist erforderlich';

  @override
  String get emailInvalidError => 'Bitte gib eine gültige E-Mail-Adresse ein';

  @override
  String get passwordFieldLabel => 'Passwort';

  @override
  String get passwordRequiredError => 'Passwort ist erforderlich';

  @override
  String get passwordLengthError =>
      'Passwort muss mindestens 6 Zeichen lang sein';

  @override
  String get noAccountQuestion => 'Noch kein Konto?';

  @override
  String get hasAccountQuestion => 'Bereits ein Konto?';

  @override
  String get signupAction => 'Registrieren';

  @override
  String get loginAction => 'Anmelden';

  @override
  String get loginButton => 'Anmelden';

  @override
  String get signupButton => 'Konto erstellen';

  @override
  String get phoneVerificationHeader => 'Telefon verifizieren';

  @override
  String get phoneVerificationHint =>
      'Du erhältst einen 6-stelligen Code zur Verifizierung deiner Telefonnummer';

  @override
  String get otpVerificationHeader => 'Verifizierungscode eingeben';

  @override
  String get otpVerificationResendCode => 'Code erneut senden';

  @override
  String get otpVerificationResendIn => 'Erneut senden in';

  @override
  String get otpVerificationVerifyButton => 'Verifizieren & Fortfahren';

  @override
  String get selectLifeSituation => 'Wähle deine Lebenssituation';

  @override
  String get lifeSituationIndividual => 'Individuum';

  @override
  String get lifeSituationIndividualDesc =>
      'Fokus auf persönliches Wachstum und Wohlbefinden';

  @override
  String get lifeSituationRelationship => 'Beziehung';

  @override
  String get lifeSituationRelationshipDesc =>
      'Bildschirmzeit mit deinem Partner ausbalancieren';

  @override
  String get lifeSituationFamily => 'Familie';

  @override
  String get lifeSituationFamilyDesc =>
      'Gesunde digitale Gewohnheiten für deine Familie schaffen';

  @override
  String get lifeSituationWork => 'Arbeit';

  @override
  String get lifeSituationWorkDesc =>
      'Produktivität steigern und digitale Ablenkungen reduzieren';

  @override
  String get lifeSituationErrorRequired => 'Bitte wähle eine Lebenssituation';

  @override
  String get lifeSituationTitle => 'Deine Lebenssituation';

  @override
  String get lifeSituationDescription =>
      'Wähle die Option, die deine aktuelle Situation am besten beschreibt';

  @override
  String get lifeSituationCustom => 'Benutzerdefiniert';

  @override
  String get rewardHistoryScreenTitle => 'Belohnungsverlauf';

  @override
  String get rewardHistoryEmptyTitle => 'Noch keine Belohnungen';

  @override
  String get rewardHistoryEmptyMessage =>
      'Du hast noch keine Belohnungen eingelöst. Schließe Herausforderungen ab, um Punkte zu sammeln und Belohnungen einzulösen.';

  @override
  String get loadRedemptionsButton => 'Belohnungen laden';

  @override
  String get goBackButton => 'Zurück';

  @override
  String activeActivityStatus(String activityTitle) {
    return '$activityTitle (Active)';
  }

  @override
  String pausedActivityStatus(String activityTitle) {
    return '$activityTitle (Paused)';
  }

  @override
  String get activeActivityMotivation =>
      'You\'re doing super! Stay strong and finish the task to earn valuable Detox Points.';

  @override
  String activeActivityHealthBenefit(String benefit) {
    return 'This activity is good for your health: $benefit';
  }

  @override
  String get activeActivityStopConfirmTitle => 'Are you sure?';

  @override
  String get activeActivityStopConfirmMessage =>
      'You\'re so close to completing this activity! Stay strong and continue to earn valuable Detox Points.';

  @override
  String get activeActivityKeepGoingButton => 'Keep Going';

  @override
  String get activeActivityStopButton => 'Stop Activity';

  @override
  String get activeActivityPauseButton => 'Pause';

  @override
  String get activeActivityResumeButton => 'Resume';

  @override
  String get settingsTitle => 'Einstellungen';

  @override
  String get appTheme => 'App-Theme';

  @override
  String get themeSystem => 'System';

  @override
  String get themeLight => 'Hell';

  @override
  String get themeDark => 'Dunkel';

  @override
  String get language => 'Sprache';

  @override
  String get notifications => 'Benachrichtigungen';

  @override
  String get enableNotifications => 'Benachrichtigungen aktivieren';

  @override
  String get notificationsDescription =>
      'Erhalten Sie Benachrichtigungen über neue Aktivitäten, Gutscheine und Erinnerungen';

  @override
  String get notifyAboutActivities => 'Aktivitätsbenachrichtigungen';

  @override
  String get activityMoodPreferences => 'Stimmungspräferenzen';

  @override
  String get otherNotifications => 'Andere Benachrichtigungen';

  @override
  String get newActivitiesPushNotif => 'Neue Aktivitäten';

  @override
  String get newCouponsPushNotif => 'Neue Gutscheine';

  @override
  String get activityRemindersPushNotif => 'Aktivitätserinnerungen';

  @override
  String get motivationalMessagesPushNotif => 'Motivierende Nachrichten';

  @override
  String get commentLikesPushNotif => 'Kommentar-Likes';

  @override
  String get noNotifications => 'Keine Benachrichtigungen';

  @override
  String get noNotificationsDescription =>
      'Du hast noch keine Benachrichtigungen. Wir werden dich über neue Aktivitäten, Belohnungen und wichtige Updates informieren.';

  @override
  String get notificationDeleted => 'Benachrichtigung gelöscht';

  @override
  String get notificationMarkedRead => 'Als gelesen markiert';

  @override
  String get notificationMarkedUnread => 'Als ungelesen markiert';

  @override
  String get undo => 'Rückgängig';

  @override
  String get activitiesScreenTitle => 'Mood & Activities';

  @override
  String get filterAndSort => 'Filter & Sort';

  @override
  String get activities => 'Activities';

  @override
  String get allActivities => 'All Activities';

  @override
  String get myActivities => 'My Activities';

  @override
  String get newest => 'Newest';

  @override
  String get mostPopular => 'Most Popular';

  @override
  String get mostUpvoted => 'Most Upvoted';

  @override
  String get highestRated => 'Highest Rated';

  @override
  String get languages => 'Languages';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String errorLoadingData(String error) {
    return 'Error loading data: $error';
  }

  @override
  String errorRefreshingData(String error) {
    return 'Error refreshing data: $error';
  }

  @override
  String get orWithEmail => 'or with email';

  @override
  String get takeDeepBreath => 'Take a deep breath';

  @override
  String get dashboardTitle => 'Dashboard';

  @override
  String get somethingWentWrong => 'Etwas ist schiefgelaufen';

  @override
  String get tryAgain => 'Erneut versuchen';

  @override
  String get dailyProgress => 'Täglicher Fortschritt';

  @override
  String get failedToLoadProgressData =>
      'Fehler beim Laden der Fortschrittsdaten';

  @override
  String get connectionProblem =>
      'Es gab ein Problem bei der Verbindung zum Server.';

  @override
  String get noMotivationalContent => 'Keine motivierenden Inhalte verfügbar';

  @override
  String get dailyPoints => 'tägliche Punkte';

  @override
  String get foodMoodTitle => 'Food Mood';

  @override
  String get filter => 'Filter';

  @override
  String get sortBy => 'Sort By';

  @override
  String get defaultOption => 'Default';

  @override
  String get topRated => 'Top Rated';

  @override
  String get newestFirst => 'Newest First';

  @override
  String get oldestFirst => 'Oldest First';

  @override
  String get filterOptions => 'Filter Options';

  @override
  String get clearAll => 'Alles löschen';

  @override
  String get foodCategories => 'Lebensmittelkategorien';

  @override
  String get hideHighlyDownvotedItems => 'Hide Highly Downvoted Items';

  @override
  String get hideItemsWithTooManyNegativeVotes =>
      'Hide items with too many negative votes';

  @override
  String get sort => 'Sortieren';

  @override
  String get loadingYourProfile => 'Loading your profile...';

  @override
  String get failedToLoadProfile => 'Fehler beim Laden des Profils';

  @override
  String get startMyDetoxJourney => 'Start My Detox Journey';

  @override
  String get startBeingPresent => 'Start Being Present';

  @override
  String get startMyDetox => 'Start My Detox';

  @override
  String get getStarted => 'Get Started';

  @override
  String get comingSoon => 'Demnächst verfügbar';

  @override
  String get detoxRewards => 'Detox-Belohnungen';

  @override
  String get newLabel => 'NEU';

  @override
  String get rewardsDescription =>
      'Absolviere Aktivitäten, um Punkte zu sammeln und gegen reale Belohnungen einzulösen';

  @override
  String get goodMorning => 'Guten Morgen';

  @override
  String get goodAfternoon => 'Guten Tag';

  @override
  String get goodEvening => 'Guten Abend';

  @override
  String get goodNight => 'Gute Nacht';

  @override
  String get currentPointsLabel => 'Aktuell';

  @override
  String get targetPointsLabel => 'Ziel';

  @override
  String get maxPointsLabel => 'Maximum';

  @override
  String get detoxActivities => 'Detox-Aktivitäten';

  @override
  String get defaultActivitiesDescription =>
      'Probiere diese Aktivitäten, um dein digitales Wohlbefinden zu verbessern und Punkte zu sammeln';

  @override
  String activitiesForMood(String mood) {
    return 'Aktivitäten für deine $mood Stimmung';
  }

  @override
  String get beginnerActivities => 'Anfänger';

  @override
  String get intermediateActivities => 'Fortgeschritten';

  @override
  String get expertActivities => 'Experte';

  @override
  String get loadingDashboard => 'Dein persönliches Dashboard wird geladen...';

  @override
  String get noActivitiesAvailable =>
      'Noch keine Aktivitäten für diese Stimmung verfügbar';

  @override
  String get activityInProgressError =>
      'Du hast bereits eine Aktivität in Bearbeitung';

  @override
  String get startActivity => 'Starten';

  @override
  String get moodOMeterTitle => 'Wie fühlst du dich heute?';

  @override
  String get moodOMeterInstructions =>
      'Ziehe den Zeiger über die Stimmungsskala oder tippe auf ein Emoji, um deine aktuelle Stimmung auszuwählen';

  @override
  String get createActivityTitle => 'Aktivität erstellen';

  @override
  String get titleLabel => 'Titel';

  @override
  String get titleHint => 'Gib einen Titel für deine Aktivität ein';

  @override
  String get titleRequired => 'Bitte gib einen Titel ein';

  @override
  String get descriptionLabel => 'Beschreibung';

  @override
  String get descriptionHint => 'Beschreibe deine Aktivität';

  @override
  String get descriptionRequired => 'Bitte gib eine Beschreibung ein';

  @override
  String durationLabel(int duration) {
    return 'Dauer: $duration Minuten';
  }

  @override
  String durationMinutes(int minutes) {
    return '$minutes Min';
  }

  @override
  String get healthBenefitsLabel => 'Gesundheitliche Vorteile';

  @override
  String get healthBenefitsHint =>
      'Beschreibe die gesundheitlichen Vorteile dieser Aktivität';

  @override
  String get healthBenefitsRequired =>
      'Bitte beschreibe die gesundheitlichen Vorteile';

  @override
  String get healthBenefitsDialogTitle => 'Gesundheitliche Vorteile';

  @override
  String get addNewHealthBenefitHint =>
      'Neuen gesundheitlichen Vorteil hinzufügen';

  @override
  String get selectedBenefitsLabel => 'Ausgewählte Vorteile:';

  @override
  String get suggestedBenefitsLabel => 'Vorgeschlagene Vorteile:';

  @override
  String get categoryLabel => 'Kategorie';

  @override
  String get languageLabel => 'Sprache';

  @override
  String get selectLanguageHint => 'Sprache auswählen';

  @override
  String get recommendedForMoodsLabel => 'Empfohlen für Stimmungen';

  @override
  String get selectAtLeastOneMood => '(wähle mindestens 1)';

  @override
  String get moodSelectionError => 'Bitte wähle mindestens 1 Stimmung aus';

  @override
  String get healthBenefitsSelectionError =>
      'Bitte wähle mindestens 2 gesundheitliche Vorteile aus';

  @override
  String get privacySettingsLabel => 'Datenschutzeinstellungen';

  @override
  String get makeActivityPrivateLabel => 'Aktivität privat machen';

  @override
  String get privateActivityDescription =>
      'Nur du kannst diese Aktivität sehen';

  @override
  String get showYourNameLabel => 'Deinen Namen anzeigen';

  @override
  String get showNameDescription => 'Dein Name wird als Ersteller angezeigt';

  @override
  String get createActivityButton => 'Aktivität erstellen';

  @override
  String get activityCreatedSuccess => 'Aktivität erfolgreich erstellt';

  @override
  String get loginRequiredError =>
      'Du musst angemeldet sein, um Aktivitäten zu erstellen';

  @override
  String createActivityError(String error) {
    return 'Fehler beim Erstellen der Aktivität: $error';
  }

  @override
  String get selectYourMood => 'Wähle deine Stimmung';

  @override
  String get commonMoods => 'Häufige Stimmungen:';

  @override
  String get allMoodOptions => 'Alle Stimmungsoptionen:';

  @override
  String get moreMoodsButton => 'Mehr';

  @override
  String get lessMoodsButton => 'Weniger';

  @override
  String get noMoodSelected => 'Keine Stimmung ausgewählt';

  @override
  String get selectMoodAbove => 'Wähle oben eine Stimmung aus';

  @override
  String get moodSelectionDescription =>
      'Wir schlagen Aktivitäten vor, die dir helfen, dich besser zu fühlen oder positiv zu bleiben';

  @override
  String get failedToLoadActivities => 'Fehler beim Laden der Aktivitäten';

  @override
  String get noActivitiesFound => 'Keine Aktivitäten gefunden';

  @override
  String get noActivitiesForMoodDescription =>
      'Wir konnten keine Aktivitäten für deine aktuelle Stimmung finden. Versuche, eine andere Stimmung auszuwählen.';

  @override
  String get noUserActivities => 'Keine von dir erstellten Aktivitäten';

  @override
  String get noUserActivitiesDescription =>
      'Du hast noch keine Aktivitäten für diese Stimmung erstellt. Erstelle eine oder deaktiviere den Filter.';

  @override
  String get showAllActivities => 'Alle Aktivitäten anzeigen';

  @override
  String suggestionsForMood(String mood, String emoji) {
    return 'Vorschläge für $mood $emoji';
  }

  @override
  String get startButton => 'Starten';

  @override
  String get stopButton => 'Stoppen';

  @override
  String activityStarted(String activityTitle) {
    return 'Gestartet: $activityTitle';
  }

  @override
  String activityStartError(String error) {
    return 'Fehler beim Starten der Aktivität: $error';
  }

  @override
  String get activityCancelled =>
      'Aktivität abgebrochen. Keine Punkte vergeben.';

  @override
  String activityCancelError(String error) {
    return 'Fehler beim Abbrechen der Aktivität: $error';
  }

  @override
  String get completeCurrentActivityFirst =>
      'Schließe zuerst die aktuelle Aktivität ab oder breche sie ab';

  @override
  String get cancelButton => 'Abbrechen';

  @override
  String get applyButton => 'Anwenden';

  @override
  String get healthBenefitStress => 'Reduziert Stress und Angst';

  @override
  String get healthBenefitCardio => 'Verbessert die Herzgesundheit';

  @override
  String get healthBenefitClarity => 'Steigert die geistige Klarheit';

  @override
  String get healthBenefitImmune => 'Stärkt das Immunsystem';

  @override
  String get healthBenefitSleep => 'Verbessert die Schlafqualität';

  @override
  String get healthBenefitStrength => 'Steigert die körperliche Kraft';

  @override
  String get healthBenefitMindful => 'Fördert die Achtsamkeit';

  @override
  String get healthBenefitDigital => 'Reduziert digitale Abhängigkeit';

  @override
  String get healthBenefitFocus => 'Verbessert Fokus und Konzentration';

  @override
  String get healthBenefitEmotional => 'Fördert das emotionale Wohlbefinden';

  @override
  String get healthBenefitEnergy => 'Steigert das Energieniveau';

  @override
  String get healthBenefitCreativity => 'Fördert die Kreativität';

  @override
  String get healthBenefitPosture => 'Verbessert die Körperhaltung';

  @override
  String get healthBenefitWeight => 'Unterstützt das Gewichtsmanagement';

  @override
  String get foodMoodExplanationTitle => 'Wusstest du?';

  @override
  String get foodMoodExplanationText =>
      'Die Nahrung, die du isst, kann deine Stimmung und dein psychisches Wohlbefinden erheblich beeinflussen. Nährstoffreiche Lebensmittel unterstützen die Gehirnfunktion und helfen, Emotionen zu regulieren, während verarbeitete Lebensmittel zu Stimmungsschwankungen beitragen können.';

  @override
  String get noRecommendationsFound => 'Keine Empfehlungen gefunden';

  @override
  String get tryDifferentMood =>
      'Versuche, eine andere Stimmung auszuwählen oder deine Filter anzupassen.';

  @override
  String get description => 'Beschreibung';

  @override
  String get benefits => 'Vorteile';

  @override
  String get categories => 'Kategorien';

  @override
  String get hideDownvotedItems => 'Stark herabgestufte Artikel ausblenden';

  @override
  String get hideDownvotedDescription =>
      'Artikel mit zu vielen negativen Bewertungen ausblenden';

  @override
  String get recommendedForMoods => 'Empfohlen für Stimmungen';

  @override
  String get nutritionalInfo => 'Nährwertinformationen';

  @override
  String get ingredients => 'Zutaten';

  @override
  String get preparationSteps => 'Zubereitungsschritte';

  @override
  String get healthyFood => 'Gesundes Essen';

  @override
  String get moodHappy => 'Glücklich';

  @override
  String get moodSad => 'Traurig';

  @override
  String get moodAngry => 'Wütend';

  @override
  String get moodAnxious => 'Ängstlich';

  @override
  String get moodTired => 'Müde';

  @override
  String get moodStressed => 'Gestresst';

  @override
  String get moodDepressed => 'Depressiv';

  @override
  String get moodExcited => 'Aufgeregt';

  @override
  String get moodCalm => 'Ruhig';

  @override
  String get moodBored => 'Gelangweilt';

  @override
  String get foodCategoryProtein => 'Eiweißreich';

  @override
  String get foodCategoryHealthyFats => 'Gesunde Fette';

  @override
  String get foodCategoryComplexCarbs => 'Komplexe Kohlenhydrate';

  @override
  String get foodCategoryVitamins => 'Vitaminreich';

  @override
  String get foodCategoryMinerals => 'Mineralien';

  @override
  String get foodCategoryOmega3 => 'Omega-3';

  @override
  String get foodCategoryProbiotics => 'Probiotika';

  @override
  String get foodCategoryCalming => 'Beruhigend';

  @override
  String get foodCategoryEnergyBoosting => 'Energie spendend';

  @override
  String get foodCategoryAntioxidants => 'Antioxidantien';

  @override
  String get activitiesTitle => 'Aktivitäten';

  @override
  String helloUser(String userName) {
    return 'Hallo, $userName';
  }

  @override
  String goalTag(String goal) {
    return '$goal';
  }

  @override
  String get familyTag => 'Familie';

  @override
  String get childrenTag => 'Kinder';

  @override
  String get motivationChildrenFocus =>
      'Du bist heute ein tolles Vorbild für deine Kinder! Deine Konzentration zeigt ihnen, wie wichtig es ist, präsent zu sein.';

  @override
  String get motivationChildrenSleep =>
      'Besserer Schlaf bedeutet, für deine Familie präsenter zu sein. Bleib dran an deinen digitalen Grenzen!';

  @override
  String get motivationChildrenGeneric =>
      'Deine Kinder merken, wenn du das Handy weglegst. Jeder Moment der Verbindung zählt!';

  @override
  String get motivationFocusHigh =>
      'Unglaubliche Konzentration heute! Dein Geist ist klar und deine Produktivität steigt. Weiter so!';

  @override
  String get motivationFocusLow =>
      'Jeder Moment fern vom Bildschirm hilft deinem Gehirn, sich besser zu fokussieren. Du schaffst das!';

  @override
  String get motivationSleep =>
      'Deine Schlafqualität verbessert sich mit jeder digitalen Pause. Körper und Geist danken es dir!';

  @override
  String get motivationGeneric =>
      'Kleine Schritte führen zu großen Veränderungen. Mit jeder digitalen Pause veränderst du dich positiv. Weiter so!';

  @override
  String get sortNotifications => 'Benachrichtigungen sortieren';

  @override
  String get byDateNewestFirst => 'Nach Datum (neueste zuerst)';

  @override
  String get unreadFirst => 'Ungelesene zuerst';

  @override
  String unreadCount(int count) {
    return '$count ungelesen';
  }

  @override
  String get today => 'Heute';

  @override
  String get yesterday => 'Gestern';

  @override
  String get delete => 'Löschen';

  @override
  String get markRead => 'Als gelesen markieren';

  @override
  String get markUnread => 'Als ungelesen markieren';

  @override
  String get couponDetails => 'Gutschein-Details';

  @override
  String get aboutPartner => 'Über den Partner';

  @override
  String get address => 'Adresse';

  @override
  String get openInMaps => 'In Karten öffnen';

  @override
  String get howToUse => 'Verwendung';

  @override
  String get termsAndConditions => 'Allgemeine Geschäftsbedingungen';

  @override
  String get validUntil => 'Gültig bis';

  @override
  String get redeemCoupon => 'Gutschein einlösen';

  @override
  String redeemFor(int points) {
    return 'Für $points Punkte einlösen';
  }

  @override
  String redeemConfirmation(String discount, String partner, int points) {
    return 'Möchten Sie einen $discount Gutschein von $partner einlösen? Dies kostet Sie $points Punkte.';
  }

  @override
  String get cancel => 'Abbrechen';

  @override
  String get redeem => 'Einlösen';

  @override
  String get openingInMaps => 'Öffne in Karten...';

  @override
  String get shareFeatureComingSoon => 'Teilen-Funktion kommt bald!';

  @override
  String get activityDetails => 'Aktivitätsdetails';

  @override
  String get activityCompleted => 'Aktivität abgeschlossen!';

  @override
  String activityCompletedMessage(String title) {
    return 'Glückwunsch! Du hast \"$title\" abgeschlossen und Punkte verdient!';
  }

  @override
  String get ok => 'OK';

  @override
  String get addToFavorites => 'Zu Favoriten hinzufügen';

  @override
  String get removedFromFavorites => 'Aus Favoriten entfernt';

  @override
  String get addedToFavorites => 'Zu Favoriten hinzugefügt';

  @override
  String get commentAdded => 'Kommentar hinzugefügt';

  @override
  String get activityInProgress => 'Aktivität läuft';

  @override
  String get remaining => 'verbleibend';

  @override
  String get elapsed => 'vergangen';

  @override
  String get stayFocused =>
      'Bleib fokussiert, um mehr Detox-Punkte zu verdienen!';

  @override
  String get activityPoints => 'Aktivitätspunkte';

  @override
  String get points => 'Punkte';

  @override
  String get completeToEarn =>
      'Schließe diese Aktivität ab, um Punkte zu verdienen!';

  @override
  String get aboutThisActivity => 'Über diese Aktivität';

  @override
  String get healthBenefits => 'Gesundheitliche Vorteile';

  @override
  String get noHealthBenefitsListed =>
      'Keine gesundheitlichen Vorteile für diese Aktivität aufgeführt.';

  @override
  String get recommendedFor => 'Empfohlen für';

  @override
  String get noCommentsYet => 'Noch keine Kommentare';

  @override
  String get beFirstToComment => 'Sei der Erste, der seine Erfahrung teilt';

  @override
  String get shareYourExperience => 'Teile deine Erfahrung...';

  @override
  String get justNow => 'gerade eben';

  @override
  String minutesAgo(int minutes) {
    return 'vor ${minutes}m';
  }

  @override
  String hoursAgo(int hours) {
    return 'vor ${hours}h';
  }

  @override
  String daysAgo(int days) {
    return 'vor ${days}d';
  }

  @override
  String get activityComplete => 'Aktivität abgeschlossen!';

  @override
  String get comments => 'Kommentare';

  @override
  String createdBy(String name) {
    return 'Erstellt von: $name';
  }

  @override
  String get detoxBenefits => 'Gesundheitliche Vorteile';

  @override
  String get targetedMoods => 'Empfohlen für';

  @override
  String started(String name) {
    return 'Gestartet: $name';
  }

  @override
  String failedToStartActivity(String error) {
    return 'Fehler beim Starten der Aktivität: $error';
  }

  @override
  String pointsValue(int value) {
    return '$value Punkte';
  }

  @override
  String get upvotes => 'Positive Bewertungen';

  @override
  String get downvotes => 'Negative Bewertungen';

  @override
  String get nutritionInfo => 'Nährwertinformationen';

  @override
  String get calories => 'Kalorien';

  @override
  String get protein => 'Protein';

  @override
  String get carbs => 'Kohlenhydrate';

  @override
  String get fats => 'Fette';

  @override
  String get moodFilter => 'Filter by Mood';

  @override
  String get networkStatusOnline => 'Online';

  @override
  String get networkStatusOffline => 'Offline';

  @override
  String get networkStatusChecking => 'Checking network...';

  @override
  String get commonLoading => 'Loading...';

  @override
  String get foodImageLoadErrorTitle => 'Image Not Available';

  @override
  String get foodImageLoadErrorMessage =>
      'Could not load image. Please try again later.';

  @override
  String get suggestionsFor => 'Suggestions for';

  @override
  String get tipsAndTricks => 'Tips & Tricks';

  @override
  String get activityTips =>
      'Regular activity breaks can boost your productivity and mental wellbeing. Try to engage in screen-free activities for at least 15 minutes every hour.';

  @override
  String get loading => 'Loading...';

  @override
  String get foodRecommendations => 'Food Recommendations';

  @override
  String get timeToDisconnect => 'Time to Disconnect';

  @override
  String get activitiesMotivationalMessage =>
      'Put your phone down and dive into real life! Every activity you complete brings you closer to breaking free from digital addiction while earning valuable Detox Points.';

  @override
  String get earnPointsForActivities =>
      'Earn points for every activity completed';

  @override
  String get details => 'Details';

  @override
  String get discussion => 'Diskussion';

  @override
  String get editComment => 'Bearbeite deinen Kommentar...';

  @override
  String get commentLikeNotification => 'Kommentar-Like-Benachrichtigungen';

  @override
  String get commentLikeNotificationDescription =>
      'Benachrichtigung erhalten, wenn jemand deinen Kommentar liked';
}
