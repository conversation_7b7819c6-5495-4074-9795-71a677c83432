import 'package:dartz/dartz.dart';
import 'package:detoxme/domain/entities/detox_points.dart';
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/detox_points_repository.dart';
import 'package:detoxme/domain/usecases/add_points_usecase.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDetoxPointsRepository extends Mock implements DetoxPointsRepository {}

void main() {
  late AddPointsUseCase useCase;
  late MockDetoxPointsRepository mockRepository;

  setUp(() {
    mockRepository = MockDetoxPointsRepository();
    useCase = AddPointsUseCase(detoxPointsRepository: mockRepository);
  });

  final testUserId = 'test-user-id';
  final testPoints = 100;
  final testReason = 'Test reason';
  final testDetoxPoints = DetoxPoints(
    id: 'test-id',
    userId: testUserId,
    userName: 'Test User',
    avatarUrl: null,
    totalPoints: 500,
    level: 5,
    availablePoints: 200,
    usedPoints: 300,
    pointsToNextLevel: 100,
    lastUpdated: DateTime.now(),
    dailyPoints: 50,
    dailyGoalTarget: 100,
    dailyDate: DateTime.now(),
  );

  test('should add points to the user account', () async {
    // Arrange
    when(
      () => mockRepository.addPoints(testUserId, testPoints, testReason),
    ).thenAnswer((_) async => Right(testDetoxPoints));

    // Act
    final result = await useCase(
      userId: testUserId,
      points: testPoints,
      reason: testReason,
    );

    // Assert
    expect(result, Right(testDetoxPoints));
    verify(
      () => mockRepository.addPoints(testUserId, testPoints, testReason),
    ).called(1);
    verifyNoMoreInteractions(mockRepository);
  });

  test('should return a failure when repository fails', () async {
    // Arrange
    final failure = ServerFailure('Failed to add points');
    when(
      () => mockRepository.addPoints(testUserId, testPoints, testReason),
    ).thenAnswer((_) async => Left(failure));

    // Act
    final result = await useCase(
      userId: testUserId,
      points: testPoints,
      reason: testReason,
    );

    // Assert
    expect(result, Left(failure));
    verify(
      () => mockRepository.addPoints(testUserId, testPoints, testReason),
    ).called(1);
    verifyNoMoreInteractions(mockRepository);
  });
}
