import 'dart:async';
import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:detoxme/core/services/sound_service.dart';
import 'package:detoxme/domain/entities/task.dart' as entity;
import 'package:detoxme/domain/failure/failure.dart';
import 'package:detoxme/domain/repositories/task_repository.dart';
import 'package:detoxme/presentation/bloc/reward/reward_cubit.dart';
import 'package:detoxme/presentation/bloc/task/task_cubit.dart';
import 'package:detoxme/presentation/bloc/task/task_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mocks
class MockTaskRepository extends Mock implements TaskRepository {}

class MockRewardCubit extends Mock implements RewardCubit {}

class MockSoundService extends Mock implements SoundService {}

void main() {
  late MockTaskRepository mockTaskRepository;
  late MockRewardCubit mockRewardCubit;
  late MockSoundService mockSoundService;
  late TaskCubit taskCubit;

  // Sample tasks
  final task1 = entity.Task(
    id: 't1',
    title: 'Task 1',
    description: 'Desc 1',
    duration: Duration(minutes: 1),
  );
  final task2 = entity.Task(
    id: 't2',
    title: 'Task 2',
    description: 'Desc 2',
    duration: Duration(minutes: 2),
  );
  final List<entity.Task> tasks = [task1, task2];

  setUpAll(() {
    // Register fallback values for any() matchers
    registerFallbackValue(task1);
  });

  setUp(() {
    mockTaskRepository = MockTaskRepository();
    mockRewardCubit = MockRewardCubit();
    mockSoundService = MockSoundService();
    taskCubit = TaskCubit(
      taskRepository: mockTaskRepository,
      rewardCubit: mockRewardCubit,
      soundService: mockSoundService,
    );

    // Default stub for taskCompleted in RewardCubit
    when(() => mockRewardCubit.taskCompleted(any())).thenAnswer((_) async {});
    // Default stub for sound service
    when(() => mockSoundService.playTaskCompleteSound()).thenAnswer((_) => {});
  });

  tearDown(() {
    taskCubit.close();
  });

  group('TaskCubit', () {
    test('initial state is TaskInitial', () {
      expect(taskCubit.state, TaskInitial());
    });

    group('loadTasks', () {
      blocTest<TaskCubit, TaskState>(
        'emits [TaskLoading, TaskLoaded] on success',
        setUp: () {
          when(
            () => mockTaskRepository.getTasks(),
          ).thenAnswer((_) async => Right(tasks));
        },
        build: () => taskCubit,
        act: (cubit) => cubit.loadTasks(),
        expect:
            () => [
              TaskLoading(),
              // Expect TaskLoaded with a non-null task (randomly selected)
              isA<TaskLoaded>()
                  .having((state) => state.tasks, 'tasks', tasks)
                  .having(
                    (state) => state.currentTask,
                    'currentTask',
                    isNotNull,
                  ),
            ],
        verify: (_) => verify(() => mockTaskRepository.getTasks()).called(1),
      );

      blocTest<TaskCubit, TaskState>(
        'emits [TaskLoading, TaskLoaded with null task] if no tasks returned',
        setUp: () {
          when(
            () => mockTaskRepository.getTasks(),
          ).thenAnswer((_) async => const Right([]));
        },
        build: () => taskCubit,
        act: (cubit) => cubit.loadTasks(),
        expect:
            () => [
              TaskLoading(),
              const TaskLoaded(tasks: [], currentTask: null),
            ],
      );

      blocTest<TaskCubit, TaskState>(
        'emits [TaskLoading, TaskError] on failure',
        setUp: () {
          when(
            () => mockTaskRepository.getTasks(),
          ).thenAnswer((_) async => const Left(CacheFailure('Failed')));
        },
        build: () => taskCubit,
        act: (cubit) => cubit.loadTasks(),
        expect:
            () => [
              TaskLoading(),
              isA<TaskError>().having(
                (state) => state.failure.message,
                'message',
                'Failed',
              ),
            ],
      );
    });

    group('getNewTask', () {
      blocTest<TaskCubit, TaskState>(
        'emits [TaskLoaded] with a new task',
        seed:
            () => TaskLoaded(
              tasks: tasks,
              currentTask: task1,
            ), // Seed with initial state
        build: () => taskCubit,
        act: (cubit) => cubit.getNewTask(),
        expect:
            () => [
              isA<TaskLoaded>()
                  .having((state) => state.tasks, 'tasks', tasks)
                  .having(
                    (state) => state.currentTask,
                    'currentTask',
                    isNotNull,
                  ), // Could be task1 or task2
            ],
      );
    });

    group('startTaskTimer / _tick / taskTimerFinished', () {
      blocTest<TaskCubit, TaskState>(
        'emits [TaskTimerActive] states and calls rewardCubit.taskCompleted on finish',
        build: () => taskCubit,
        seed:
            () => TaskLoaded(
              tasks: tasks,
              currentTask: task1,
            ), // Task duration 1 min (60 secs)
        act: (cubit) async {
          cubit.startTaskTimer(task1);
          // Wait longer than the task duration + buffer
          await Future.delayed(const Duration(seconds: 61));
        },
        expect:
            () => [
              // Expect multiple TaskTimerActive states, checking the first and maybe last
              isA<TaskTimerActive>()
                  .having((s) => s.activeTask, 'task', task1)
                  .having(
                    (s) => s.remainingDuration,
                    'initial duration',
                    const Duration(minutes: 1),
                  ),
              // ... intermediate TaskTimerActive states ...
              // Last state before timer finishes (or close to it)
              isA<TaskTimerActive>()
                  .having((s) => s.activeTask, 'task', task1)
                  .having(
                    (s) => s.remainingDuration.inSeconds,
                    'almost done',
                    lessThanOrEqualTo(1),
                  ),
              // Expect TaskLoaded after timer finishes and getNewTask is called
              isA<TaskLoaded>().having(
                (state) => state.currentTask,
                'new task',
                isNotNull,
              ),
            ],
        verify: (_) {
          // Verify taskCompleted was called on the reward cubit
          verify(() => mockRewardCubit.taskCompleted(task1)).called(1);
        },
        // Set timeout for test involving Future.delayed
        wait: const Duration(seconds: 62),
      );
    });

    group('cancelTimer', () {
      blocTest<TaskCubit, TaskState>(
        'emits [TaskLoaded] with the previously active task when timer is cancelled',
        build: () => taskCubit,
        seed: () => TaskTimerActive(task1, const Duration(seconds: 30)),
        act: (cubit) => cubit.cancelTimer(),
        expect:
            () => [
              isA<TaskLoaded>().having((s) => s.currentTask, 'task', task1),
            ],
      );
    });
  });
}
