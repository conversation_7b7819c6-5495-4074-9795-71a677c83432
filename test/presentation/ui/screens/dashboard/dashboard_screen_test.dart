import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_cubit.dart';
import 'package:detoxme/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:detoxme/presentation/ui/screens/dashboard/dashboard_screen.dart';
import 'package:detoxme/presentation/ui/widgets/weather_card.dart';

// Mock classes
class MockDashboardCubit extends MockCubit<DashboardState>
    implements DashboardCubit {}

void main() {
  late MockDashboardCubit mockDashboardCubit;

  setUp(() {
    mockDashboardCubit = MockDashboardCubit();
  });

  // Helper function to build the DashboardScreen with necessary providers
  Widget buildDashboardScreen() {
    return MaterialApp(
      home: BlocProvider<DashboardCubit>.value(
        value: mockDashboardCubit,
        child: const DashboardScreen(),
      ),
    );
  }

  group('DashboardScreen', () {
    testWidgets('shows loading indicator when status is loading', (
      WidgetTester tester,
    ) async {
      // Arrange - Set up the state
      when(
        () => mockDashboardCubit.state,
      ).thenReturn(const DashboardState(status: DashboardStatus.loading));

      // Act - Build the widget
      await tester.pumpWidget(buildDashboardScreen());

      // Assert - Verify expected widgets
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading your dashboard...'), findsOneWidget);
    });

    testWidgets('shows error view when status is failure', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(() => mockDashboardCubit.state).thenReturn(
        const DashboardState(
          status: DashboardStatus.failure,
          errorMessage: 'Test error message',
        ),
      );

      // Act
      await tester.pumpWidget(buildDashboardScreen());

      // Assert
      expect(find.text('Something went wrong'), findsOneWidget);
      expect(find.text('Test error message'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('shows dashboard content when status is success', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(() => mockDashboardCubit.state).thenReturn(
        const DashboardState(
          status: DashboardStatus.success,
          userName: 'Test User',
          hasFamily: true,
          hasChildren: false,
          goal: 'Focus',
          currentPoints: 450,
          maxPoints: 1000,
          targetPoints: 750,
          weatherCondition: WeatherCondition.sunny,
          temperature: 25,
          location: 'Test City',
          timeOfDay: TimeOfDay(hour: 12, minute: 0),
        ),
      );

      // Act
      await tester.pumpWidget(buildDashboardScreen());

      // Assert
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.byType(WeatherCard), findsOneWidget);
      expect(find.text('Your Progress'), findsOneWidget);
      expect(find.text('Test City'), findsOneWidget);
      expect(find.text('450'), findsAtLeastNWidgets(1)); // Current points
    });

    testWidgets('clicking add points button calls updatePoints', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(() => mockDashboardCubit.state).thenReturn(
        const DashboardState(
          status: DashboardStatus.success,
          userName: 'Test User',
          currentPoints: 450,
          maxPoints: 1000,
        ),
      );
      when(
        () => mockDashboardCubit.updatePoints(any()),
      ).thenAnswer((_) async => {});

      // Act
      await tester.pumpWidget(buildDashboardScreen());
      await tester.pump(); // Wait for animations

      // Find and tap the "Add Points" button
      final addPointsButton = find.text('Add Points');
      expect(addPointsButton, findsOneWidget);
      await tester.tap(addPointsButton);
      await tester.pump();

      // Assert
      verify(() => mockDashboardCubit.updatePoints(50)).called(1);
    });
  });
}
