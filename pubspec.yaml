name: detoxme
description: "DetoxMe: A Flutter project for healthy habits."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.1.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.5
  supabase_flutter: ^2.8.4
  shared_preferences: ^2.5.3
  go_router: ^15.0.0
  dartz: ^0.10.1 # Functional programming helpers
  crypto: ^3.0.3   # For hashing
  equatable: ^2.0.5 # For value equality
  connectivity_plus: ^6.1.3
  audioplayers: ^6.0.0 # For playing audio
  flutter_svg: ^2.0.10 # For SVG support
  animate_do: ^4.2.0 # Added for animations
  flutter_dotenv: ^5.1.0 # Added for environment variables
  flutter_native_splash: ^2.4.6
  video_player: ^2.8.6 # Added for onboarding videos
  http: ^1.2.1
  get_it: ^8.0.3
  app_links: ^6.0.1 # For deep linking
  url_launcher: ^6.3.0 # To launch URLs
  google_fonts: ^6.2.1 # Added for custom fonts
  sign_in_with_apple: ^7.0.1 # Added for Apple Sign-In
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  device_info_plus: ^11.4.0
  flutter_local_notifications: ^19.1.0
  country_code_picker: ^3.3.0
  pinput: ^5.0.1
  sms_autofill: ^2.4.1
  lottie: ^3.1.0 # For Lottie animations
  live_activities: ^2.3.1 # For iOS Live Activities
  percent_indicator: ^4.2.5
  speedometer_chart: ^1.0.8
  geolocator: ^14.0.0
  shimmer: ^3.0.0
  provider: ^6.1.4
  fl_chart: ^0.64.0
  timelines_plus: ^1.0.6
  package_info_plus: ^8.3.0 # For app version information
  floaty_nav_bar: ^1.0.2
  zo_animated_border: ^0.0.7 # For animated borders on active activities
  cached_network_image: ^3.3.0

  uuid: any
  bloc: any
  country_flags: ^3.2.0
  dotlottie_loader: ^0.0.5
  flutter_cache_manager: ^3.4.1
  flutter_masonry_view: ^0.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Bloc/Cubit testing utility
  bloc_test: ^10.0.0

  # Mocking library
  mocktail: ^1.0.3

  # Launcher icon generator
  flutter_launcher_icons: ^0.14.3

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # Enable localization generation
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/svg/
    - assets/audio/
    - assets/animations/
    - assets/images/splash_logo.png # Added splash logo
    - assets/videos/ # Added path for onboarding videos
    # - assets/audio/ # Assuming this is still not needed
    - env/.env.dev    # Updated path
    - env/.env.prod   # Updated path
    - env/.env.example # Assuming this might be created later in env/
    - shorebird.yaml
    # Add other asset paths here if needed

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Configuration for flutter_native_splash
flutter_native_splash:
  # Background color for the splash screen
  color: "#f5eec4"

  # The image asset to show on the splash screen
  image: assets/images/splash_logo.png

  # Optional: customize how the image fits
  android_gravity: center
  ios_content_mode: scaleAspectFit # Changed from scaleAspectFill to fit image within bounds

  # Optional: Android 12+ specific settings (highly recommended)
  android_12:
    color: "#f5eec4"
    image: assets/images/splash_logo.png
    # icon_background_color: "#f5eec4" # Optional: background for the icon itself

  # Optional: You can disable splash for specific platforms
  # android: false
  # ios: false
  # web: false
